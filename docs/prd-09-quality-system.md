# PRD-09: 质量管理子系统 (QMS) 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: Roo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
在激烈的市场竞争中，稳定可靠的产品质量是企业赢得客户信任的基石。传统的质量管理多依赖于人工检查和纸质记录，存在标准不统一、检验过程不透明、质量数据难于追溯和分析等问题。一旦出现客户投诉或批量质量事故，往往难以快速定位问题根源。本子系统（QMS）旨在建立一套覆盖从来料、生产过程到最终成品的全面质量管控体系，将质量标准数字化，将检验过程流程化，将质量数据结构化，实现质量管理的闭环。

### 1.2 商业价值
- **提升产品质量稳定性**: 通过标准化的检验流程和数字化标准，减少人为因素导致的质量波动。
- **降低质量成本**: 及时发现并处理不合格品，避免其流入下一环节或客户端，从而降低返工、报废和客户索赔带来的损失。
- **实现精准质量追溯**: 当出现质量问题时，能根据产品批次，快速追溯到其使用的原材料批次、生产工序、操作人员、检验记录等全部信息。
- **持续改进质量**: 通过对质量数据的统计分析（如合格率、缺陷分布），为工艺改进和供应商优化提供数据驱动的决策依据。

### 1.3 关键目标
- **目标1**: 建立统一的质量检验标准库，支持对不同物料、产品、工序定义定性和定量的检验项目。
- **目标2**: 实现来料检验（IQC）、过程检验（IPQC）、最终检验（FQC）三大核心检验业务的流程化。
- **目标3**: 与采购、生产、仓储等核心模块深度集成，将质量检验无缝嵌入到业务流程中。
- **目标4**: 建立规范的不合格品处理流程，并对质量数据进行有效的统计和分析。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 王检
- **角色**: **质检员 (QC)**
- **背景**: 负责车间的日常质量检验工作，包括原材料入库检验和生产过程中的巡检。
- **核心诉-求**:
    - "我希望能在一个地方（比如平板电脑上）看到今天所有待检的任务，而不是拿着一堆单子在车间里跑。"
    - "检验的时候，我希望能清晰地看到这个东西要检哪些项目，标准是什么，而不是全凭记忆或去翻手册。"
    - "发现不合格品时，我希望能方便地记录下来，并通知到相关负责人，而不是靠口头或微信通知。"

### 2.2 核心场景
- **场景一：来料检验 (IQC)**
  1.  **触发**: 仓库对一批采购到货的玻璃原片执行“收货”操作。
  2.  **系统响应**:
      - WMS系统通知QMS系统，有一批物料待检。
      - QMS系统自动根据物料类型，匹配预设的“玻璃原片来料检验方案”，生成一张IQC检验单，并推送到质检员王检的待办任务列表。
  3.  **操作路径**:
      - 王检在他的PDA或平板上看到该任务，前往待检区。
      - 打开检验单，系统清晰列出检验项目：外观（有无划痕、气泡）、尺寸（长、宽、厚度）、对角线等。
      - 他逐项进行检验，对定量项目（如尺寸），直接输入测量值；对定性项目（如外观），选择“合格”或“不合格”。
      - 检验完成后，他输入本次检验的抽样数、合格数、不合格数，并提交检验结果。
  4.  **结果**:
      - **检验合格**: QMS通知WMS，该批次物料状态变为“合格”，仓管员可以进行“上架”操作。
      - **检验不合格**: 物料状态变为“不合格”，自动触发“不合格品处理流程”，仓管员无法将其上架到正品库。
  5.  **期望**: 检验任务自动触发，检验标准清晰明确，检验结果实时影响库存状态，形成闭环。

- **场景二：生产过程巡检 (IPQC)**
  1.  **触发**: 生产订单中，“钢化工序”设置了过程检验点。当一批玻璃完成钢化，工人报工后。
  2.  **系统响应**: QMS自动为这批在制品生成一张IPQC检验单。
  3.  **操作路径**:
      - 王检收到任务，到钢化炉旁进行巡检。
      - 打开检验单，检验项目可能包括：表面应力、抗冲击测试（抽检）、碎片状态等。
      - 录入检验结果并提交。
  4.  **结果**:
      - **检验合格**: 该批在制品可以流转到下一道工序。
      - **检验不合格**: 触发不合格品处理流程，该批在制品被隔离，生产主管会收到通知，决定是返工还是报废。
  5.  **期望**: 质量控制点能嵌入到生产流程中，及时发现过程问题，防止批量不合格品的产生。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 质量标准管理 (F-QM-01)
- **用户故事 (F-QM-01-01)**: 作为一名**质量工程师**，我想要**定义和管理检验项目和检验方案**，以便**为不同物料和工序设定标准化的质量要求**。
  - **详细描述**:
    - **正常流程**:
      1. **检验项目库**: 支持创建检验项目，定义项目名称、类型（定量/定性）、标准值/要求、上/下限公差、所用仪器等。
      2. **检验方案**: 支持创建检验方案，一个方案由多个检验项目组成。
      3. **方案关联**: 支持将检验方案与物料（用于IQC/FQC）或工艺路线中的工序（用于IPQC）进行关联。

### 3.2 检验业务执行 (F-QM-02)
- **用户故事 (F-QM-02-01)**: 作为一名**质检员**，我想要**在一个统一的界面接收和执行所有类型的检验任务**，以便**高效地完成日常检验工作**。
  - **详细描述**:
    - **正常流程**:
      1. **检验单自动生成**:
         - 采购收货 -> 自动生成IQC检验单。
         - 生产工序报工 -> 自动生成IPQC检验单。
         - 生产完工入库 -> 自动生成FQC检验单。
      2. **检验执行**: 质检员在PDA或PC端打开检验单，逐项录入检验结果（测量值或判定结论）。
      3. **检验判定**: 系统根据录入结果和预设标准，自动判定单项和整单的检验结果（合格/不合格/让步接收）。

### 3.3 不合格品处理 (F-QM-03)
- **用户故事 (F-QM-03-01)**: 作为一名**质量主管**，我想要**对所有检验不合格的物料/产品进行评审和处置**，以便**形成规范的不合格品处理闭环**。
  - **详细描述**:
    - **正常流程**:
      1. 检验不合格后，系统自动生成一张“不合格品处理单”，进入待处理池。
      2. 质量主管或相关人员打开处理单，进行评审，并选择处置方式：报废、返工、降级、退货。
      3. **报废**: 触发WMS的报废出库流程。
      4. **返工**: 触发MES生成返工生产订单。
      5. **退货**: 触发采购模块的采购退货流程。

### 3.4 质量追溯与分析 (F-QM-04)
- **用户故事 (F-QM-04-01)**: 作为一名**质量工程师**，我想要**通过批次号追溯产品的全流程质量信息**，以便**在出现问题时快速定位根源**。
  - **详细描述**:
    - **正常流程**:
      1. 提供一个质量追溯平台，输入成品批次号。
      2. 系统能正向追溯到该批次产品的FQC检验报告、IPQC检验报告、发货记录等。
      3. 系统能反向追溯到其所使用的半成品/原材料的批次号，以及这些批次的IQC检验报告和供应商信息。

- **用户故事 (F-QM-04-02)**: 作为一名**质量主管**，我想要**查看多维度的质量统计报表**，以便**分析质量趋势，驱动持续改进**。
  - **详细描述**:
    - **正常流程**:
      1. 提供各类统计报表，如：供应商来料合格率排名、工序/产品缺陷分布柏拉图、不合格品处置成本分析等。
      2. 支持按时间、供应商、物料、工序等维度进行筛选和钻取。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **集成性**: 必须与采购、生产、仓储模块实现无缝、实时的双向数据交互。质量检验的结果必须能立刻阻塞或放行相关业务流程。
- **可靠性**: 检验数据作为产品档案的核心部分，必须保证其准确性和不可篡改性。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **检验项目 (QC_Test_Item)**
    - `item_id` (PK)
    - `item_code`
    - `item_name`
    - `item_type` (定量/定性)
    - `standard_value`
    - `upper_limit`
    - `lower_limit`
    - ...

2.  **检验方案 (QC_Test_Plan)**
    - `plan_id` (PK)
    - `plan_name`
    - `linked_object_type` (物料, 工序)
    - `linked_object_id`
    - (关联到检验项目)

3.  **检验单 (QC_Inspection_Order)**
    - `order_id` (PK)
    - `order_type` (IQC, IPQC, FQC)
    - `source_doc_id` (收货单, 生产工单)
    - `item_id` (待检物料)
    - `lot_number` (待检批次)
    - `status` (待检, 在检, 已检)
    - `result` (合格, 不合格)
    - ...

4.  **检验单明细 (QC_Inspection_Order_Line)**
    - `line_id` (PK)
    - `order_id` (FK)
    - `test_item_id` (FK)
    - `test_result_value`
    - `test_result_conclusion` (合格/不合格)
    - ...

5.  **不合格品处理单 (Nonconforming_Product_Report)**
    - `ncr_id` (PK)
    - `inspection_order_id` (FK)
    - `description`
    - `disposal_method` (报废, 返工, 退货)
    - `status` (待处理, 已处理)
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 质量检验嵌入业务流程
```mermaid
graph TD
    subgraph 采购域
        A[采购到货] --> B[WMS: 收货];
        B --> C[QMS: 生成IQC检验单];
        C --> D{IQC检验};
        D -- 合格 --> E[WMS: 入库上架];
        D -- 不合格 --> F[触发不合格品处理];
    end
    subgraph 生产域
        G[上游工序完工] --> H[MES: 工人报工];
        H --> I[QMS: 生成IPQC检验单];
        I --> J{IPQC检验};
        J -- 合格 --> K[流转至下游工序];
        J -- 不合格 --> F;
        L[成品完工] --> M[QMS: 生成FQC检验单];
        M --> N{FQC检验};
        N -- 合格 --> O[WMS: 成品入库];
        N -- 不合格 --> F;
    end
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-QM-01 (IQC流程闭环)**:
  - **Given** 我在WMS中对一张采购订单执行收货，该物料关联了IQC检验方案
  - **When** 我尝试在IQC检验完成前，对该批次物料进行上架
  - **Then** WMS系统必须阻止我上架，并提示“该批次物料待检，无法操作”。

- **AC-QM-02 (IPQC流程阻塞)**:
  - **Given** 生产订单的“钢化”工序需要IPQC，且当前检验结果为“不合格”
  - **When** 下游“组装”工序的工人尝试领取这批“钢化”后的半成品
  - **Then** MES或WMS系统必须阻止其领料，并提示“该批次物料检验不合格”。

- **AC-QM-03 (质量追溯)**:
  - **Given** 我有一个成品批次号
  - **When** 我在质量追溯平台输入该批次号
  - **Then** 系统必须能展示出该成品的所有IPQC和FQC报告，并能进一步链接到其所用原材料的批次号和IQC报告。