# PRD-08: 项目制管理子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: Roo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
对于防火窗、酒店隔断、幕墙工程等业务，其经营模式本质上是项目制的。这类业务周期长、交付复杂、涉及环节多（设计、生产、安装、售后），且需要对整个项目的成本和利润进行独立核算。传统的ERP系统往往侧重于离散制造，难以有效管理这种以项目为核心的业务流程。本子系统旨在提供一个强大的项目管理平台，实现对工程类业务从立项、计划、执行到交付收款的全生命周期精细化管控。

### 1.2 商业价值
- **提升项目可视性**: 提供项目看板和甘特图，让管理者能清晰地掌握每个项目的进度、成本和风险，告别信息黑盒。
- **精确核算项目利润**: 将所有与项目相关的收入、成本（材料、人工、费用、分包）精确归集到项目上，为项目盈利能力分析提供准确数据。
- **加强多阶段交付管理**: 规范和追踪项目各阶段（如窗框、固定玻璃、窗扇）的生产、交付和安装流程，确保项目按计划有序推进。
- **协同内外部资源**: 有效协同设计、采购、生产、施工等内部团队，并管理好分包商等外部资源。

### 1.3 关键目标
- **目标1**: 建立支持多层级结构的项目主数据模型，以适应不同工程业务的分解需求。
- **目标2**: 实现项目级的计划与进度管理，支持WBS（工作分解结构）和关键里程碑的设定与追踪。
- **目标3**: 构建项目成本归集的核心能力，确保所有项目相关费用都能被准确追踪。
- **目标4**: 实现与现有产销模块的深度集成，项目中的物料需求能无缝驱动采购和生产。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 周总
- **角色**: **项目经理**
- **背景**: 负责管理多个防火窗工程项目。他需要同时协调客户、设计团队、生产车间、采购部门和现场施工队，确保项目在预算内按时交付。
- **核心诉求**:
    - "我需要一个驾驶舱，能让我一眼就看出我负责的所有项目，哪些进度正常，哪些已经延期，哪些成本快超支了。"
    - "一个项目要分三批交货，我希望能清晰地给工厂下达这三批的生产指令，并追踪每一批货的交付情况。"
    - "月底我要给老板汇报项目情况，我希望能一键导出项目的成本分析报告，而不是让财务和采购给我提供一大堆零散的数据。"

### 2.2 核心场景
- **场景一：创建一个防火窗项目并分解任务**
  1.  **触发**: 公司中标一个新的办公楼防火窗项目。
  2.  **操作路径**:
      - 项目经理周总登录系统，进入【项目管理】->【项目列表】，新建项目。
      - 填写项目基本信息（项目名称、客户、合同金额、预计起止日期）。
      - 在项目工作台，周总开始分解项目结构：
          - 创建一级节点：1号楼、2号楼。
          - 在“1号楼”下，创建二级节点：1层、2层...10层。
          - 在“1层”下，根据设计图纸，录入需要交付的产品：窗户A型x10套，窗户B型x5套。
      - 接下来，周总进入“项目计划”视图，创建WBS任务：
          - **阶段一**: 设计与深化（里程碑：图纸确认）。
          - **阶段二**: 生产制造，并分解为“窗框生产”、“玻璃生产”、“窗扇生产”。
          - **阶段三**: 现场交付与安装，并分解为“窗框交付安装”、“玻璃交付安装”、“窗扇交付安装”。
      - 为每个任务设置计划起止时间、负责人和预算。
  3.  **期望**: 项目结构的创建能灵活反映实际的工程层级，WBS分解清晰，计划制定直观。

- **场景二：项目驱动生产与采购**
  1.  **触发**: 项目计划进入“窗框生产”任务。
  2.  **操作路径**:
      - 周总在WBS中，选中“窗框生产”任务，点击“下达生产需求”。
      - 系统自动汇总该任务关联的所有产品（如1号楼1-10层的所有窗户）的BOM，并筛选出窗框所需的物料（铝型材等）。
      - 这些物料需求被传递到MRP运算模块，并打上“某某项目专用”的标签。
      - 采购员在运行MRP时，能看到这些带有项目标签的需求，并为其创建关联到该项目的采购订单。
      - 计划员也能看到这些需求，并为其创建关联到该项目的生产订单。
  3.  **期望**: 项目的需求能自动、准确地传递给下游的采购和生产系统，且所有相关的单据都与项目号关联，方便成本归集。

- **场景三：追踪项目成本和进度**
  1.  **触发**: 项目进行到一半，周总需要了解项目的健康状况。
  2.  **操作路径**:
      - 周总打开项目的“成本分析”视图。
      - 系统自动从各模块汇总数据：
          - **实际材料成本**: 所有关联到此项目的采购入库单和生产领料单的金额总和。
          - **实际人工成本**: 所有关联到此项目的生产任务所汇报的工时 * 工价。
          - **其他费用**: 财务录入的、与此项目相关的差旅、分包等费用。
      - 系统将实际成本与预算进行实时对比，并高亮显示超支项。
      - 周总切换到“进度跟踪”视图，以甘特图的形式查看所有WBS任务的计划与实际完成情况。
  3.  **期望**: 项目数据实时、透明，能一站式地完成对项目进度和成本的监控。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 项目主数据管理 (F-PM-01)
- **用户故事 (F-PM-01-01)**: 作为一名**项目经理**，我想要**创建和维护一个多层级的项目结构**，以便**精确地管理到楼栋、楼层、房间的产品交付清单**。
  - **详细描述**:
    - **正常流程**:
      1. 支持项目立项，创建项目主数据，包含项目编码、名称、客户、合同金额、项目经理等。
      2. 支持在项目下创建灵活的、不限层级的树状结构（WBS基础），用于管理交付地点和产品清单。
      3. 支持将销售订单或报价单直接关联到项目。

### 3.2 项目计划与进度 (F-PM-02)
- **用户故事 (F-PM-02-01)**: 作为一名**项目经理**，我想要**使用WBS（工作分解结构）来规划项目任务和里程碑**，以便**对项目执行进行有效的计划和控制**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建WBS任务，设置任务的计划起止时间、工期、前置任务、负责人。
      2. 支持设置关键里程碑节点。
      3. 提供甘特图视图，可视化展示项目计划和任务依赖关系。
      4. 支持填报任务的实际进度（完成百分比），并在甘特图上实时体现。

### 3.3 项目成本管理 (F-PM-03)
- **用户故事 (F-PM-03-01)**: 作为一名**项目经理**，我想要**系统能自动归集所有与项目相关的成本**，以便**实时监控项目预算执行情况和核算最终利润**。
  - **详细描述**:
    - **正常流程**:
      1. **成本归集**:
         - 采购订单、生产领料单、工序报工、费用报销单等业务单据，均可关联到具体的项目WBS任务。
         - 系统提供一个项目成本仪表盘，按“材料费”、“人工费”、“制造费用”、“分包费”、“其他费用”等科目，实时汇总项目的实际发生成本。
      2. **成本预算**: 支持为项目WBS任务编制预算成本。
      3. **预实对比**: 提供项目成本的预算与实际执行情况的对比分析报告。

### 3.4 项目交付管理 (F-PM-04)
- **用户故事 (F-PM-04-01)**: 作为一名**项目经理**，我想要**根据项目计划，分阶段、分批次地安排生产和发货**，以便**满足复杂的现场交付要求**。
  - **详细描述**:
    - **正常流程**:
      1. 支持从项目WBS节点（如“1号楼窗框”）圈选产品清单，一键生成对应的生产需求和发货通知。
      2. 生成的生产订单和销售发货单，都自动关联回源头的项目WBS任务。
      3. 支持追踪每个交付批次的状态（待生产、生产中、已发货、已签收）。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: 项目甘特图在500个WBS任务下，拖拽、展开/折叠等操作应保持流畅。项目成本报表在归集1000张源头单据时，应在10秒内计算完成。
- **集成性**: 与销售、工艺、采购、生产、仓储、财务等所有核心模块深度双向集成。
- **灵活性**: 项目的WBS结构和层级需支持高度自定义，以适应不同类型工程的管理需求。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **项目主数据 (Project_Master)**
    - `project_id` (PK)
    - `project_code` (Unique)
    - `project_name`
    - `customer_id`
    - `contract_amount`
    - `project_manager_id`
    - `status` (进行中, 已完工, 已关闭)
    - ...

2.  **项目结构节点 (Project_Structure_Node)**
    - `node_id` (PK)
    - `project_id` (FK)
    - `parent_node_id` (FK)
    - `node_name` (e.g., 1号楼, 1层)
    - `node_type` (楼栋, 楼层, 房间)
    - ...

3.  **项目交付清单 (Project_Delivery_List)**
    - `delivery_item_id` (PK)
    - `node_id` (FK)
    - `item_id` (FK, 产品)
    - `quantity`
    - ...

4.  **WBS任务 (WBS_Task)**
    - `wbs_task_id` (PK)
    - `project_id` (FK)
    - `task_name`
    - `planned_start_date`
    - `planned_end_date`
    - `actual_progress`
    - `budgeted_cost`
    - ...

5.  **项目成本明细 (Project_Cost_Detail)**
    - `cost_detail_id` (PK)
    - `project_id` (FK)
    - `wbs_task_id` (FK)
    - `cost_type` (材料, 人工, 费用)
    - `amount`
    - `source_doc_type` (采购单, 领料单, 报销单)
    - `source_doc_id`
    - `transaction_date`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 项目制业务端到端流程
```mermaid
graph TD
    A[销售/投标: 项目立项] --> B[项目经理: 创建项目主数据];
    B --> C[分解项目结构<br/>(楼栋/楼层/产品)];
    B --> D[制定WBS计划<br/>(甘特图)];
    D -- 按计划 --> E{触发下游业务};
    E -- 生产需求 --> F[关联项目的生产订单];
    E -- 采购需求 --> G[关联项目的采购订单];
    E -- 费用需求 --> H[关联项目的费用报销];
    F & G & H --> I{成本数据自动归集};
    I --> J[项目成本中心];
    D -- 追踪 --> K[项目进度];
    J & K --> L[项目经理: 监控项目健康状况];
    C -- 按交付计划 --> M[仓储: 分批发货];
    M --> N[现场安装/签收];
    N --> O[财务: 项目收款/结算];
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-PM-01 (项目结构创建)**:
  - **Given** 我是一名项目经理
  - **When** 我创建一个新项目，并在其下创建了“1号楼”->“1层”->“窗户A”这样的三级结构
  - **Then** 在项目结构树中，必须能清晰地看到这个层级关系。

- **AC-PM-02 (成本自动归集)**:
  - **Given** 我有一个项目P001，并为它创建了一张金额为10000元的采购订单
  - **When** 这张采购订单被审核通过并入库
  - **Then** 在项目P001的成本分析报告中，“实际材料成本”必须自动增加10000元。

- **AC-PM-03 (分批交付)**:
  - **Given** 项目P001的“1层”需要10套窗户A，“2层”需要5套窗户B
  - **When** 我在WBS中只选择“1层”的交付清单，并下达发货指令
  - **Then** 系统生成的发货通知单中，必须只包含10套窗户A，而不能包含窗户B。