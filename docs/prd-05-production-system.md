# PRD-05: 智能制造数字化工厂核心运营平台 (Smart Manufacturing Digital Factory Core Operating Platform) 产品需求文档

> **版本**: 2.1 (高保真原型设计版)
> **状态**: 需求已确认
> **撰写人**: 产品经理 (Roo)
> **日期**: 2025-07-30
> **行业聚焦**: 玻璃深加工及高附加值细分领域

---

## 0. 文档引言 (Introduction)

### 0.1 修订历史 (Revision History)
| 版本号 | 主要变更内容 | 修订人 | 日期 |
| :--- | :--- | :--- | :--- |
| 2.0 | 初始架构设计稿 | 资深MES产品架构师 | 2025-07-29 |
| 2.1 | **[当前版本]** 针对UI/UX和开发团队，进行全面的功能规格、交互细节和业务规则扩充，使其成为可用于高保真原型设计的PRD。 | 产品经理 (Roo) | 2025-07-30 |

### 0.2 面向读者 (Target Audience)
本篇PRD主要面向以下团队成员：
- **UI/UX 设计师**: 用于理解业务流程、用户场景和界面交互需求，从而进行高保真界面设计。
- **前端工程师**: 用于了解详细的功能规格、数据逻辑、API交互和异常处理，作为前端开发的直接依据。
- **后端工程师**: 用于理解业务逻辑、数据模型和集成需求，配合完成API接口设计与开发。
- **测试工程师**: 用于设计测试用例，确保产品功能符合需求规格。
- **项目经理**: 用于了解项目范围、核心功能和验收标准，进行项目管理和进度跟踪。

### 0.3 本次迭代范围界定 (Scope Definition for This Iteration)

为了确保V2.0核心价值的快速交付，本次高保真原型设计及后续开发将聚焦于以下核心模块。

#### **包含范围 (In-Scope)**
1.  **智能生产调度中心 (F-PD-1)**:
    *   [x] 订单自动分解为工序任务。
    *   [x] 支持定义多目标、多约束的排程场景。
    *   [x] "一键式"智能排程功能的核心流程。
    *   [x] 以甘特图形式可视化呈现排程结果。
    *   [x] 对排程结果进行关键指标解读（计划摘要）。
    *   [x] 支持对计划进行有限的人工干预与模拟推演。
2.  **智能车间执行中心 (F-PD-2)**:
    *   [x] 工位终端的任务队列展示与数字化SOP查阅。
    *   [x] 基于扫码的工序报工（含合格、不合格上报）。
    *   [x] 关键工序（钢化/夹胶）的过程数据自动采集（模拟）。
    *   [x] 面向车间主管的实时电子看板（进度、Andon、质量）。
3.  **全生命周期质量追溯 (F-PD-3)**:
    *   [x] UID的生成与绑定流程。
    *   [x] 关键工序扫码关联操作信息。
    *   [x] 质量追溯门户的核心查询与“数字档案”展示功能。
4.  **设备物联与数据驱动决策 (F-PD-4)**:
    *   [x] OEE核心指标的计算与可视化驾驶舱。
    *   [x] 停机原因的柏拉图分析。

#### **暂不包含范围 (Out-of-Scope)**
- **[!] 高级算法的深度配置**: 本次迭代不提供APS算法参数的图形化配置界面，算法参数将由后端配置。
- **[!] 预测性维护 (F-PD-4.3)**: 预测性维护的复杂建模与预警功能，本次仅做数据采集，不在原型中实现其预警和工单联动逻辑。
- **[!] 完整的系统集成对接**: 与ERP、WMS、PLM的接口将通过模拟数据或Mock API的方式进行，不进行真实的端到端联调。
- **[!] 人员技能矩阵管理**: 排程约束中的人员技能约束，本次迭代简化为默认所有人员技能满足要求。
- **[!] 计件工资核算**: MES到ERP的工时上报接口本次不实现。
- **[!] 复杂的权限管理界面**: 本次迭代仅实现核心角色的权限控制，不提供灵活的权限配置界面。

---

## 1. 愿景与战略目标 (Vision & Strategic Goals)

### 1.1 新愿景：从“数字化车间”到“智能制造核心平台”
V1.0版本的MES成功地将车间管理带入了数字化时代。现在，V2.0的目标是实现一次质的飞跃：将MES从一个被动的**生产执行系统**，升级为一个主动的、具备感知、决策和优化能力的**数字化工厂核心平台**。它将不再仅仅是记录生产活动，而是成为驱动整个工厂运营的“智能大脑”，深度赋能玻璃深加工及其高附加值延伸领域（如系统防火窗、高端酒店沐浴房、定制化幕墙玻璃等）的复杂制造场景。

### 1.2 商业价值主张
- **驱动卓越运营**: 通过全局最优的智能调度，最大化资源利用率（设备、物料、人力），最小化生产周期与成本，实现极致的运营效率。
- **赋能价值创造**: 以全生命周期质量追溯能力，满足高端产品（如防火窗）的强制性认证要求，提升品牌价值和客户信任度。
- **数据驱动决策**: 将生产现场的“哑”数据转化为业务洞察，为工艺优化、成本控制、预测性维护等提供量化依据，实现从“经验驱动”到“数据驱动”的转变。
- **构建生态中枢**: 作为连接企业管理层（ERP）、设计端（PLM/CAD）和物理世界（设备/产线）的核心枢-纽，打破信息壁垒，实现端到端的业务流程协同。

### 1.3 四大核心战略目标
- **目标1：构建智能调度与执行引擎 (Intelligent APS & Execution)**: 设计一套能处理高度定制化、小批量、多品种订单的智能排程引擎。该引擎需能自动分解订单，并综合考虑切裁优化、复杂工艺路径和多重约束，生成并下发全局最优的生产计划。
- **目标2：建立全生命周期质量追溯体系 (Lifecycle Quality Traceability)**: 建立从原片到成品的精细化质量追溯链。为每一片玻璃赋予唯一数字身份（UID），关联其在各工序的所有工艺参数、设备、人员及质检结果，以满足最严苛的追溯要求。
- **目标3：深化设备物联与数据洞察 (IIoT & Data Insight)**: 实现MES与核心设备的深度集成，完成生产指令的自动下发和工艺参数的自动采集。并通过对设备数据的分析，实现OEE监控、能耗管理和预测性维护。
- **目标4.0：强化系统生态集成 (Ecosystem Integration)**: 明确定义MES作为工厂运营中枢，与ERP、PLM/CAD/CAM、WMS等关键子系统的双向数据流和业务协同逻辑，为BI提供核心生产数据，支撑管理驾驶舱。

---

## 2. 用户画像与核心场景 (V2.0) (User Personas & Core Scenarios)

### 2.1 升级的用户画像
| 画像 (Persona) | 角色 (Role) | 背景与职责 | 核心诉求 (Key Needs) |
| :--- | :--- | :--- | :--- |
| **陈工** | **生产计划与调度工程师** | 从前的“车间主管”，现在是生产的“总参谋”。负责管理APS（高级计划与排程）引擎，设定优化目标和约束条件，并处理复杂异常。 | "我不再需要手动拖任务。我需要告诉系统，这批防火窗订单交期最重要，那批幕墙订单成本最重要，让系统给我一个全局最优的生产计划。" <br><br> "当CNC2号机突然故障，我需要系统在1分钟内告诉我重排后的最优方案是什么。" |
| **李师傅** | **智能化产线操作员** | 在设备旁的工业平板上工作，接收来自MES的指令，监控设备状态，并对生产过程中的关键数据进行确认。 | "我的任务不是思考怎么做，而是精准地执行。MES告诉我这炉钢化玻璃的工艺曲线ID是C-105，我扫码加载并确认执行就行。" <br><br> "设备参数有异常，系统会自动报警并暂停，还会提示我联系设备工程师。" |
| **王经理** | **质量与工艺工程师** | 负责建立和维护全生命周期质量追溯体系。定义各工序需要采集的工艺参数，并利用追溯数据进行持续改进。 | "客户投诉某批次防火窗的耐火性能，我需要输入产品ID，在30秒内调出它从原片到成品的所有数据：包括钢化炉的实际升温曲线、夹胶高压釜的压力记录、以及当时的操作员。" |
| **赵总** | **工厂运营总监** | 关注工厂整体的运营效率和效益，通过BI驾驶舱来做决策。 | "我需要实时看到我们工厂的OEE（设备综合效率）是多少，哪个环节是瓶颈。我还要知道，上周我们因为设备非计划停机损失了多少潜在产值。" |

### 2.2 “智能制造”核心场景
- **场景一：APS引擎的“一键排程”与计划下发**
  1.  **触发**: 一周内积累了50个销售订单，涵盖上千种不同规格的玻璃，工艺路径各异（切、磨、钻、钢、夹、中）。
  2.  **操作路径**:
      - 计划工程师陈工进入【智能生产调度中心】。
      - 他设定本周的**核心优化目标**：1. 优先保证防火窗订单的交期；2. 最大化原片套裁利用率；3. 尽可能合并钢化炉次。
      - 点击“启动智能排程”。
  3.  **系统响应**:
      - **(分析与建模)**: APS引擎自动分解所有订单，并读取设备产能日历、实时物料库存、人员技能矩阵等约束。
      - **(运算与优化)**: 在云端或本地服务器上，复杂的优化算法在几分钟内完成百万级的运算，生成一个满足所有约束且在目标上最优的生产计划。
      - **(结果呈现)**: 系统以甘特图形式呈现计划，并附上一份“计划分析报告”，说明此计划如何达成了设定的优化目标（如：预计套裁率92%，防火窗订单预计全部提前1天完成）。
      - **(指令下发)**: 陈工确认计划后，点击“下发计划”。所有工序任务（包含加工程序、工艺参数ID）被自动推送到对应工位或设备的任务列表里。
  4.  **期望**: 将计划员数小时乃至数天的工作，缩短为几分钟的“设定目标+确认结果”，实现真正的智能决策。

- **场景二：基于“数字身份”的全生命周期质量追溯**
  1.  **触发**: 某重点幕墙项目的客户，要求提供其采购的 Lot-A 批次中，任意一块玻璃的完整生产过程数据作为交付物。
  2.  **操作路径**:
      - 质量工程师王经理在成品库中，扫描其中一块玻璃上由激光刻印的二维码，获取其唯一身份ID (UID): `PN-20250729-LOT-A-135`。
      - 他进入【全生命周期质量追溯】系统，输入此UID。
  3.  **系统响应**:
      - 系统呈现一个可视化的“数字档案”报告：
          - **源头**: 该玻璃切割自原片批次 `YP-20250720-002` (附IQC报告链接)。
          - **切割**: 由1号切割台在 `[时间]` 执行，操作员 `李工`，所用套裁方案 `CS-101`。
          - **钢化**: 在2号钢化炉 `[时间]` 完成，炉次号 `SG-256`，系统自动关联并展示了该炉次完整的**温度-时间曲线图**。
          - **组装**: 在中空生产线 `[时间]` 与另一块玻璃 (UID: `...136`) 合片，使用胶批次 `JJ-098`，当时线边仓环境温湿度为 `25°C, 45%`。
          - **检验**: 通过了IPQC和FQC的所有检验项 (附检验数据详情)。
          - **交付**: 已于 `[时间]` 打包至铁架 `TJ-068`并发往 `[项目地址]`。
  4.  **期望**: 实现单片级别的、不可篡改的、贯穿全流程的精细化质量追溯，将质量管理提升到全新高度。

- **场景三：基于IIoT的预测性维护**
  1.  **触发**: MES的设备健康度监控模块，侦测到3号CNC加工中心的主轴电机电流在过去两周内持续小幅攀升，已超出正常基线模型的95%置信区间。
  2.  **系统响应**:
      - 系统自动生成一个“预测性维护预警”，并发送给设备主管和操作员李师傅。
      - 预警信息包含：`设备：3号CNC，部件：主轴电机，潜在风险：轴承磨损或负载异常，建议措施：安排在本周末停机检修，检查轴承润滑情况`。
      - 同时，系统自动在【备品备件】模块中检查对应型号的轴承库存，并提示“库存充足”。
      - 设备主管据此创建一张“设备维护工单”，并安排维修任务。
  3.  **期望**: 从“坏了再修”的被动响应，转变为“提前预警、主动维护”，最大程度减少非计划停机时间，保障生产的连续性。

---

## 3. 功能需求 V2.0：智能制造核心 (Functional Requirements V2.0)

### F-PD-1: 智能生产调度中心 (Intelligent Production Scheduling Center)
这是MES v2.0的核心，它将取代原有的生产订单、手动排程和独立切割优化模块，成为一个统一的、由数据和算法驱动的决策中心。

- **用户故事 (F-PD-1.1)**: 作为一名**计划工程师**，我想要**系统能自动将已确认的销售订单分解为可执行的工序级任务池**，以便**为智能排程提供标准化的输入**。
    - **功能规格 (Functional Specifications)**:
        1.  **数据同步**: 系统需能通过后台服务，准实时地从ERP/PLM系统中同步已审核通过的销售订单及对应的生产BOM和工艺路线。
        2.  **订单分解触发**:
            *   **自动触发**: 系统后台服务定时轮询（如每5分钟）新增的合格订单，并自动执行分解。
            *   **手动触发**: 在“智能生产调度中心”主界面，提供一个“同步并分解新订单”的按钮，允许计划工程师手动触发此过程。
        3.  **分解逻辑**:
            *   对于每一个销售订单行，系统根据其关联的工艺路线（Routing），为路线中的每一道工序（Operation）生成一个独立的工序任务（Work Order Task）。
            *   每个生成的工序任务必须包含以下关键信息，继承自订单和BOM：`产品编码`, `产品名称`, `规格尺寸`, `数量`, `所属销售订单号`, `客户名称`, `期望交付日期`, `工序号`, `工序名称`, `标准工时`, `工作中心`等。
            *   工序任务之间需建立明确的前后置依赖关系（Finish-to-Start）。
        4.  **任务池管理**: 所有新生成的工序任务，其初始状态为“待排程”，并被放入一个全局的“待排程任务池”中。
        5.  **异常处理与通知**:
            *   **数据校验**: 在分解前，系统必须校验订单数据的完整性，特别是“工艺路线”和“标准工时”是否存在。
            *   **分解失败**: 如果校验失败，该销售订单将被标记为“分解异常”，并从本次分解流程中跳过。
            *   **异常列表**: 在“智能生产调度中心”界面，需要有一个“异常订单”列表，清晰展示所有分解失败的订单及其原因（如：“订单SO20250801-015，产品PN-A002：缺少工艺路线”）。
            *   **通知机制**: 系统需向工艺部门的角色（如工艺工程师）发送站内通知或邮件，提示其处理异常数据。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **主界面**: 此功能主要为后台逻辑，但在“智能生产调度中心”主界面需要以下元素支撑：
        *   **按钮 (Button)**:
            *   `[同步并分解新订单]`
            *   **交互**: 点击后，按钮变为“正在分解...”并呈现加载中状态（Loading Spinner），后台开始执行分解逻辑。
            *   **成功反馈**: 操作完成后，弹出全局提示（Toast Notification）：“成功分解 X 个新订单，生成 Y 个工序任务。Z 个订单存在异常。”
            *   **失败反馈**: 若操作失败（如API连接中断），弹出错误提示：“操作失败，请联系系统管理员。”
        *   **视图/标签页 (View/Tab)**:
            *   `[待排程任务池]` (默认视图): 以表格形式展示所有状态为“待排程”的工序任务。
            *   `[异常订单列表]`
            *   **交互**: 点击此标签页，切换到异常订单列表视图。列表应清晰展示 `订单号`, `产品信息`, `异常原因`, `接收时间`。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **唯一性**: 只有在ERP中状态为“已审核”且在PLM中BOM为“已固化”的订单才能被同步。
        *   **幂等性**: 多次执行分解操作，对于同一个已成功分解的订单，不应重复生成工序任务。系统需记录每个订单的分解状态。
        *   **数据继承**: 工序任务的所有信息必须准确无误地继承自源头订单、BOM和工艺路线，保证数据一致性。

- **用户故事 (F-PD-1.2)**: 作为一名**计划工程师**，我想要**定义一个包含多重约束和多维目标的排程场景**，以便**让APS引擎理解我的业务优先级**。
    - **功能规格 (Functional Specifications)**:
        1.  **创建排程场景**:
            *   允许用户创建一个新的排程场景，并为其命名（如“第35周防火窗紧急排程”）。每个场景都是一次独立的排程计算。
        2.  **圈选待排程任务**:
            *   系统需提供一个功能强大的筛选器，允许用户从“待排程任务池”中，根据一个或多个条件组合筛选任务，条件包括：`订单交付日期范围`, `客户名称`, `产品类型`, `销售订单号`, `物料编码`等。
            *   用户可以全选筛选结果，或手动勾选/取消勾选特定的任务，将其加入到当前排程场景中。
        3.  **定义优化目标**:
            *   系统需提供一个预设的优化目标列表，如 `订单准时交付率`, `钢化炉同炉合并率`, `切割原片利用率`, `总生产周期最小化`, `设备负载均衡`。
            *   用户可以通过拖拽的方式调整目标的优先级顺序。
            *   用户可以为每个目标设置一个相对权重值（如通过滑块从0到100进行设置）。
        4.  **配置约束条件**:
            *   **自动加载**: 系统自动加载全局默认的约束条件。
            *   **设备约束**: 展示参与排程的工作中心的产能日历（可用班次、计划性停机）。用户可临时调整某个设备在本次排程中的可用性（如临时屏蔽某个设备）。
            *   **物料约束**: 系统自动对圈选任务进行物料齐套检查（M-ATP），并清晰展示物料缺口。
            *   **人员约束**: （本次迭代简化）默认人力充足。
        5.  **物料冲突预警**:
            *   在正式开始排程前，如果系统检测到圈选任务所需的物料存在严重短缺（库存+在途 < 需求，且预计到货日晚于需求日），必须给出明确的、阻塞性的告警。
            *   告警信息应清晰列出所有短缺的物料及其缺口数量，并提供建议操作，如“从本次排程中移除相关任务”或“调整相关订单的期望交付日期”。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **界面布局**: 采用多栏式布局。左侧为“待排程任务池”表格，中间为“当前排程场景配置区”，右侧为“约束与告警区”。
        *   **待排程任务池 (左侧)**:
            *   **筛选器 (Filter Bar)**: 包含上述所有筛选字段的输入框或下拉选择器。
            *   **表格 (Table)**: 展示筛选后的工序任务。列头应包括 `复选框`, `任务ID`, `产品名称`, `订单号`, `客户`, `交期`等。
            *   **按钮**: `[>> 添加至场景]`，点击后将左侧表格中所有勾选的任务移动到中间的“场景任务列表”中。
        *   **排程场景配置区 (中间)**:
            *   **输入框 (Input)**: `场景名称`。
            *   **表格 (Table)**: `场景任务列表`，展示已圈选的任务。
            *   **可拖拽列表 (Draggable List)**: `优化目标`。每个列表项包含 `目标名称` 和一个 `权重滑块 (Slider)`。用户可上下拖动以调整优先级。
        *   **约束与告警区 (右侧)**:
            *   **卡片 (Card)**: `设备约束`，点击可展开查看或临时调整设备日历。
            *   **卡片 (Card)**: `物料约束`，默认显示“物料充足 ✅”。当检测到短缺时，变为红色告警状态“物料短缺 ❗”，并展示详情。
            *   **告警弹窗 (Modal Dialog)**: 当出现阻塞性物料短缺告警时，弹出一个模态窗口，强制用户处理。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **场景隔离**: 每个排程场景的计算是相互独立的，修改一个场景的配置不应影响其他场景。
        *   **任务状态**: 被添加到场景中的任务，其状态可临时标记为“待排程（已锁定）”，防止在其他地方被重复操作。
        *   **权重归一化**: 后端APS引擎在计算时，可能会对前端传入的权重值进行归一化处理。
        *   **约束的强制性**: 硬约束（如设备不可用、物料没有）必须被严格遵守，APS引擎不能生成违反硬约束的计划。软约束（优化目标）则是在满足硬约束的前提下尽力达成。

- **用户故事 (F-PD-1.3)**: 作为一名**计划工程师**，我想要**一键启动智能排程引擎，让系统自动生成全局最优的生产计划**，以便**取代繁琐、低效的手动调度工作**。
    - **功能规格 (Functional Specifications)**:
        1.  **触发排程**: 在排程场景配置完成后，用户点击“开始智能排程”按钮，向后端APS引擎提交排程请求。
        2.  **异步计算**: 排程计算是一个耗时过程（可能需要数分钟），必须采用异步任务处理机制。前端提交请求后，应能立即获得一个任务ID，并轮询该任务的状态。
        3.  **嵌入式切割优化**: 作为APS求解的前置步骤，系统需自动调用切割优化服务，对所有切割类任务进行组合优化，计算出最优的套裁方案、预估工时和精确的母材需求。此结果将作为APS引擎的核心输入。
        4.  **APS求解**: 后端APS引擎根据场景中定义的任务、目标和约束，运行复杂的优化算法，生成一份包含所有任务的 `scheduled_start_time` 和 `scheduled_end_time` 的生产计划。
        5.  **结果呈现**:
            *   计算完成后，系统以交互式甘特图（Gantt Chart）的形式，在主界面可视化呈现生成的计划。
            *   甘特图应以工作中心（设备）为泳道，以时间为横轴，任务块的长度代表计划工时。
            *   同时，系统需生成一份“计划摘要”报告，量化展示本次计划的关键绩效指标（KPIs），如：`预计订单准时交付率`, `平均生产周期`, `各工作中心负载率`, `切割原片综合利用率`, `关键瓶颈分析`等。
        6.  **无解/冲突处理**:
            *   如果APS引擎在现有约束下无法找到满足所有硬约束（特别是订单交期）的可行解，系统不能直接失败。
            *   应生成一个“部分可行”的计划，并在甘特图和摘要中，明确高亮标记出那些延期的订单或任务。
            *   同时，需提供冲突分析报告，指出导致延期的主要原因（如：“2号钢化炉产能不足，导致订单SO-xxx延期2天”）。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **按钮 (Button)**: `[开始智能排程]`
            *   **交互**: 点击后，按钮变为“排程计算中...”，并显示一个进度条或加载动画。用户在此期间可以离开此页面。计算完成后，系统应通过站内信或桌面通知提醒用户。
        *   **甘特图 (Gantt Chart)**:
            *   **泳道 (Swimlane)**: 按工作中心/设备分组。
            *   **任务块 (Task Bar)**: 鼠标悬停（Hover）在任务块上时，显示该任务的详细信息Tooltip（`任务ID`, `产品`, `订单号`, `计划起止时间`）。
            *   **依赖线 (Dependency Line)**: 可视化展示任务之间的前后置关系。
            *   **延期高亮**: 无法按时完成的任务块，需用醒目的颜色（如红色）进行高亮。
            *   **工具栏 (Toolbar)**: 提供时间轴缩放（日/周/月视图）、“定位到今天”等功能。
        *   **侧边栏/面板 (Sidebar/Panel)**: `计划摘要报告`
            *   以数据卡片和微图表的形式，清晰展示上述KPIs。

- **用户故事 (F-PD-1.4)**: 作为一名**计划工程师**，我想要**对智能生成的计划进行人工干预和模拟推演**，以便**在最终下发前应对各种特殊情况**。
    - **功能规格 (Functional Specifications)**:
        1.  **手动调整**: 在甘特图上，允许用户通过拖拽（Drag & Drop）的方式，调整单个任务的开始时间。
        2.  **实时影响分析 (Live Impact Analysis)**:
            *   当一个任务被拖拽调整后，系统必须立即（在前端或通过快速后端调用）重新计算所有受其影响的下游任务的排程，并在甘特图上实时更新这些任务的位置。
            *   所有被联动影响的任务块，需用不同的颜色（如黄色）进行高亮，以示区分。
            *   “计划摘要”面板中的KPIs（特别是交付率和成本）也必须实时更新，以反映手动调整带来的影响。
        3.  **冲突检测**: 如果手动调整导致了硬约束冲突（如将任务A拖到了其所需物料到货之前），系统应禁止该操作，并将任务块弹回原位，同时给出明确的错误提示。
        4.  **多版本模拟 (What-If Analysis)**:
            *   用户可以将当前（无论是系统生成还是手动调整后）的计划保存为一个命名快照，如“版本A：优先保交期”、“版本B：牺牲部分交期保成本”。
            *   系统需提供一个版本对比视图，可以并排比较不同版本计划的甘特图和关键KPIs，帮助用户决策。
        5.  **计划锁定与下发**:
            *   当用户对某个版本的计划满意后，可点击“锁定并下发”按钮。
            *   此操作将把该版本计划中所有任务的状态从“待排程”更新为“已下发”，并将其推送至车间执行层的任务队列中。
            *   已下发的计划将变为只读状态，原则上不允许再修改。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **甘特图交互**:
            *   **拖拽**: 任务块可在其泳道内水平拖拽。
            *   **视觉反馈**: 拖拽时，光标变为移动手势，受影响的下游任务实时高亮。
        *   **版本管理面板**:
            *   **按钮**: `[保存为新版本]`
            *   **下拉列表 (Dropdown)**: `选择要查看或对比的版本`。
            *   **按钮**: `[对比所选版本]`
        *   **主操作按钮**:
            *   `[锁定并下发计划]`
            *   **交互**: 点击后，弹出一个确认对话框（Confirmation Dialog）：“您确定要下发‘版本B’计划吗？下发后将无法修改。”，用户确认后执行下发操作。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **调整的边界**: 人工调整只能改变任务的开始时间，不能改变其所属工作中心或计划工时。
        *   **数据一致性**: 手动调整后，所有任务的 `scheduled_start_time` 和 `scheduled_end_time` 必须被更新并保存。
        *   **版本控制**: 每个保存的计划版本，都应独立存储其完整的任务排程数据。
        *   **状态机**: 任务的状态流转应严格遵循：`待排程` -> `已下发` -> `执行中` -> `已完成`。只有“已下发”状态的任务才会出现在车间终端。

### F-PD-2: 智能车间执行中心 (Smart Workshop Execution Center)
这是连接“计划”与“现实”的桥梁。它确保最优的计划能被精准、高效、透明地执行，并实时反馈现场的真实数据，形成“计划-执行-反馈”的闭环。

- **用户故事 (F-PD-2.1)**: 作为一名**产线操作员**，我想要**在工位终端上清晰地看到我的任务队列，并能一键获取所有生产所需信息**，以便**我能专注于高质量的执行**。
    - **功能规格 (Functional Specifications)**:
        1.  **用户登录**: 操作员通过工号和密码（或刷卡）登录特定工位的工业平板终端（Station HMI）。
        2.  **任务队列加载**: 登录后，系统自动加载并显示分配到该工位（工作中心）的、状态为“已下发”的任务列表。
        3.  **任务排序**: 任务列表必须严格按照“计划开始时间”（`scheduled_start_time`）升序排列，确保操作员总是先看到最紧急的任务。
        4.  **任务详情展示**: 点击列表中的任意任务，可以进入任务详情界面。该界面必须包含一个清晰的、模块化的信息展示区。
        5.  **开工操作**:
            *   在任务详情页，提供“开工”按钮。
            *   点击“开工”后，系统将该任务的状态从“已下发”更新为“执行中”，并记录 `actual_start_time`。
            *   对于与设备集成的工位，此操作可配置为同时向设备发送“启动”或“加载程序”的指令。
        6.  **数字化生产资料查阅**:
            *   任务详情页需提供清晰的入口（如标签页或按钮）来访问关联的生产资料。
            *   **电子图纸 (Drawing)**: 支持手势缩放、平移、旋转。关键尺寸和公差应高亮显示。
            *   **作业指导书 (SOP)**: 以图文并茂的步骤形式展示，支持翻页。
            *   **工艺参数 (Parameters)**: 清晰列出本次任务需要设置或确认的关键工艺参数及其标准值/范围。
        7.  **开工前置检查**:
            *   在点击“开工”时，系统必须执行前置条件检查。
            *   **物料检查**: （与WMS集成）检查该工单所需的物料是否已配送到线边仓。如未到位，则禁止开工。
            *   **设备状态检查**: （与IIoT集成）检查对应设备是否处于“待机”或“正常”状态。如设备处于“故障”或“维护”中，则禁止开工。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **登录界面 (Login Screen)**:
            *   **输入框**: `工号`, `密码`。
            *   **按钮**: `[登录]`。
        *   **任务队列界面 (Task List View)**:
            *   **列表 (List)**: 每个列表项为一个任务卡片（Task Card）。
            *   **任务卡片**: 应简洁地展示核心信息：`计划开始时间`, `产品名称`, `规格`, `数量`, `所属订单号`。紧急任务或延期任务应有特殊颜色标记。
        *   **任务详情界面 (Task Detail View)**:
            *   **头部信息 (Header)**: 展示任务的ID，产品信息等。
            *   **主操作按钮 (Primary Button)**: `[开工]`，按钮应大而醒目。开工后，变为 `[完成报工]` 和 `[异常上报]`。
            *   **信息查阅区 (Tabbed Pane)**:
                *   `[生产信息]`: 默认标签页，显示订单、产品、数量等基本信息。
                *   `[电子图纸]`: 内嵌图纸查看器。
                *   `[作业指导书]`: 显示SOP内容。
                *   `[工艺参数]`: 显示参数列表。
            *   **状态栏 (Status Bar)**: 清晰显示当前任务状态，如“待开工”、“执行中”。
        *   **禁止开工提示 (Modal Dialog)**:
            *   当开工前置检查失败时，弹出一个模态对话框，清晰说明原因（如：“物料 [M-001] 未到位，请联系班组长！”），并提供一个“我知道了”按钮关闭对话框。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **工位绑定**: 每个终端设备在后台需与一个唯一的工作中心（Work Center）绑定。
        *   **任务可见性**: 操作员只能看到分配给其所在工作中心的任务。
        *   **顺序执行**: 原则上鼓励操作员按顺序执行任务，但系统不应强制禁止其选择稍后的任务开工（除非有特殊工艺依赖）。
        *   **状态原子性**: 任务状态的变更（如下发->执行中）必须是原子操作，确保数据一致性。

- **用户故事 (F-PD-2.2)**: 作为一名**产线操作员**，我想要**通过扫码或与设备联动的方式，自动或半自动地完成数据上报**，以便**将我从繁琐的录入工作中解放出来**。
    - **功能规格 (Functional Specifications)**:
        1.  **完成报工**:
            *   在任务详情界面，操作员完成作业后点击“完成报工”按钮。
            *   系统弹出一个报工窗口，要求输入 `合格数量` 和 `不合格数量`。
            *   如果 `不合格数量` > 0，则必须选择 `不合格原因`（如“尺寸错误”、“外观瑕疵”等，原因列表需可配置）。
            *   提交报工后，系统更新任务状态为“已完成”，并记录 `actual_end_time` 和报工数据。
        2.  **设备自动报工**:
            *   对于已集成的自动化设备，当设备完成一个加工循环并输出产量信号时，系统自动累计产量。
            *   操作员在任务结束时，只需在报工窗口中确认由设备自动填入的产量数据，并补充不合格信息即可。
        3.  **过程数据自动采集**:
            *   对于已集成的关键设备（如钢化炉），当任务“执行中”，系统后台自动从设备采集关联的过程数据（如温度曲线），并将其与该工序任务记录进行绑定。此过程对操作员透明。
        4.  **异常上报**:
            *   在任务“执行中”，操作员可随时点击“异常上报”按钮。
            *   弹出一个包含预设停机原因列表的窗口（如 `设备故障`, `待料`, `质量问题`, `寻找工具` 等，列表可配置）。
            *   选择原因后，系统开始计时，并将设备状态更新为“停机”。当操作员恢复生产时，再次点击按钮结束计时。此数据用于OEE分析。
        5.  **数据校验**:
            *   报工的总数量（合格+不合格）与任务计划数量差异超过阈值（如20%，可配置）时，系统应弹出警告，要求操作员二次确认。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **报工弹窗 (Modal Dialog)**:
            *   **数字输入框 (Number Input)**: `合格数量` (默认为任务计划数), `不合格数量` (默认为0)。
            *   **下拉选择框 (Dropdown)**: `不合格原因` (当不合格数量>0时，为必选项)。
            *   **按钮**: `[确认报工]`。
        *   **异常上报弹窗 (Modal Dialog)**:
            *   **单选按钮组 (Radio Buttons)**: `停机原因列表`。
            *   **按钮**: `[确认并开始计时]`。
        *   **二次确认对话框 (Confirmation Dialog)**:
            *   当报工数量异常时，弹出：“警告：报工总数与计划数差异较大，您确定要提交吗？” 提供 `[是，确认提交]` 和 `[否，返回修改]` 按钮。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **报工与库存**: 成功的报工记录将触发向WMS或ERP回传完工信息，以更新半成品或成品库存。
        *   **数据绑定**: 所有上报的数据（产量、不良、停机）都必须与具体的工序任务ID、操作员ID、设备ID牢固绑定。
        *   **OEE数据源**: 操作员上报的停机原因是计算OEE时间稼动率的关键输入。

- **用户故事 (F-PD-2.3)**: 作为一名**车间主管**，我想要**通过车间电子看板，实时掌握生产进度、设备状态和质量异常**，以便**快速发现问题并进行现场调度**。
    - **功能规格 (Functional Specifications)**:
        1.  **数据实时性**: 看板上的所有数据必须准实时更新（数据刷新频率应低于1分钟）。
        2.  **生产进度模块**:
            *   以订单为单位，用进度条（Progress Bar）的形式展示每张生产订单的总体完成进度（已完成工时 / 总标准工时）。
            *   高亮显示已延期或即将延期的订单。
        3.  **设备状态地图 (Andon) 模块**:
            *   以工厂车间布局图的形式，可视化展示所有核心设备。
            *   每个设备图标根据其实时状态，用不同颜色显示：`绿色-运行中`, `黄色-待机/空闲`, `红色-故障/报警`, `灰色-离线`。
            *   点击设备图标，可钻取查看该设备的详细信息，包括：`当前加工任务`, `OEE（当日）`, `运行时长`, `停机时长`等。
        4.  **质量异常快报模块**:
            *   实时滚动播报最新的不合格品记录。
            *   信息应包含：`发生时间`, `工序`, `产品名称`, `不良现象`, `数量`。
        5.  **安灯呼叫模块**:
            *   当设备状态变为“红色”时，除了图标变色，还应在该模块中出现一条安灯呼叫记录，并可配置触发声音和灯光报警。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **界面布局**: 通常为针对大屏幕设计的、信息密度高的多区块网格布局。
        *   **生产进度列表 (List)**:
            *   **列表项**: `订单号`, `客户`, `产品`, `进度条`, `状态` (如正常/将延期/已延期)。
        *   **设备地图 (SVG/Canvas)**:
            *   **设备图标 (Icon)**: 颜色根据状态变化。故障时可增加闪烁效果。
            *   **钻取弹窗 (Modal Dialog)**: 点击图标后，弹出展示设备详细数据的弹窗。
        *   **质量快报 (Marquee/Ticker)**:
            *   无交互，信息自动向上滚动。
        *   **安灯呼叫列表 (List)**:
            *   **列表项**: `呼叫时间`, `设备名称`, `状态` (如“待响应”、“处理中”)。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **数据聚合**: 看板是数据的消费者，它从MES的各个模块（任务、设备、质量）中聚合数据进行展示。
        *   **角色适配**: 看板应设计为免登录或使用通用公共账户登录，专门用于车间大屏展示。
        *   **可配置性**: 看板上展示哪些订单、哪些设备，应支持后台配置。

---

### F-PD-3: 全生命周期质量追溯 (Full Lifecycle Quality Traceability)
该模块旨在为每一片独立的玻璃产品建立一个完整、精细、不可篡改的“数字身份档案”，以满足高端产品（尤其是防火窗等安全认证产品）对质量追根溯源的严苛要求。

- **用户故事 (F-PD-3.1)**: 作为一名**工艺工程师**，我想要**系统能在玻璃被切割时，自动为每一片独立的成品或半成品生成一个全局唯一的身份ID (UID)**，以便**为后续所有的数据关联提供一个锚点**。
    - **功能规格 (Functional Specifications)**:
        1.  **UID预生成**:
            *   当一个“切割优化任务”成功生成排版方案后，系统必须为排版方案中的**每一片有效玻璃**（排版图中的每一个矩形）预先生成一个全局唯一的UID。
            *   此过程在后台自动完成，无需人工干预。
        2.  **UID编码规则**:
            *   UID的编码规则需要在后台支持可配置，但本次迭代默认规则为：`[父项产品编码]-[生产批次号]-[4位流水号]`。例如：`P00123-*************`。
        3.  **UID与任务的关联**:
            *   生成的UID列表必须与“切割优化任务”以及更上游的“生产工序任务”进行关联。
        4.  **标签打印与绑定**:
            *   在切割工位的HMI终端上，当一个切割任务完成后，操作员需要为实际产出的每一片好玻璃进行“标签打印”操作。
            *   HMI界面需展示该任务预生成的UID列表。操作员每取下一片玻璃，就点击一次“打印标签”按钮。
            *   系统通过连接到HMI的标签打印机，打印出一张包含该UID的二维码或条形码的不干胶标签。
            *   操作员将标签粘贴到对应的玻璃上，完成物理绑定。
        5.  **破损处理 (作废)**:
            *   如果在切割或取片过程中发生破损，操作员应在HMI上选择对应的UID，并点击“报废”按钮。
            *   系统将该UID的状态标记为“已作废”，并要求操作员选择报废原因。此UID将不能再用于后续任何工序。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **切割工位HMI界面**:
            *   **视图**: 当一个切割任务完成后，界面应展示一个清晰的UID列表。
            *   **UID列表 (List)**:
                *   **列表项**: `UID`, `产品尺寸`, `状态 (待打印/已打印/已作废)`。
                *   **交互**: 点击列表项，可以选中该UID。
            *   **按钮 (Button)**:
                *   `[打印选中项标签]`: 点击后，向标签打印机发送打印指令。对应列表项的状态变为“已打印”。
                *   `[报废选中项]`: 点击后，弹出报废原因选择框，确认后，对应列表项的状态变为“已作废”，并以删除线样式显示。
                *   `[全部打印]`: 一次性打印所有“待打印”状态的UID标签。
        *   **报废原因弹窗 (Modal Dialog)**:
            *   **下拉选择框**: `报废原因` (如“崩边”、“裂纹”、“尺寸错误”)。
            *   **按钮**: `[确认报废]`。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **UID唯一性**: 系统必须在数据库层面保证UID字段的唯一性约束。
        *   **状态机**: UID的状态应遵循严格的流转：`预生成` -> `已绑定(已打印)` 或 `已作废`。
        *   **打印机集成**: HMI需要通过驱动或SDK与标签打印机进行集成。打印的标签模板（尺寸、内容布局）应可配置。
        *   **数据关联**: UID一旦生成，即成为追溯链的起点，后续所有工序的数据都将围绕它进行关联。

- **用户故事 (F-PD-3.2)**: 作为一名**产线操作员或质检员**，我想要**在执行工序或检验时，通过扫描玻璃上的UID码，将当前的操作信息自动关联到这片玻璃的档案中**，以便**实现数据的无感采集**。
    - **功能规格 (Functional Specifications)**:
        1.  **扫码即关联**: 在各工序的HMI终端上，当一个任务处于“执行中”状态时，操作员可通过连接到HMI的扫码枪，扫描玻璃上的UID标签。
        2.  **数据自动绑定**:
            *   成功扫描后，系统后台自动创建一个关联记录，将该UID与当前的 `工序任务ID`、`操作员ID`、`设备ID`、`扫描时间` 等信息绑定。
            *   对于关键工序（如钢化），系统还应自动关联上由设备采集到的、与当前时间窗口匹配的 `工艺曲线ID`。
            *   对于需要消耗关键辅料的工序（如夹胶），HMI界面应提示操作员扫描辅料（如胶片）的批次码，并将其与UID关联。
        3.  **IPQC/FQC检验关联**:
            *   质检员在进行检验时，首先扫描玻璃的UID，然后执行检验流程。
            *   检验完成后，生成的检验报告（包含所有检验项结果）将自动与该UID关联。
        4.  **异常处理**:
            *   **UID校验**: 系统必须校验扫描的UID是否合法（存在、状态为“已绑定”）。
            *   **工序校验**: 系统需校验该UID对应的产品，其工艺路线是否包含当前工序。如果扫描了一个不应出现在本工序的玻璃（跳站），系统应给出明确告警。
            *   **重复扫描**: 在同一工序重复扫描同一个UID，系统应提示“该产品已在本工序记录”，并忽略重复操作。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **工位HMI界面 (任务执行中)**:
            *   **显示区域**: 界面上需要有一个醒目的区域，显示“请扫描产品UID”。
            *   **反馈**: 成功扫描一个UID后，界面上可以显示一个临时的成功提示（如“UID: ...123 已记录”），并在一个列表中显示本任务已扫描的所有UID。
            *   **辅料扫描提示**: 需要扫描辅料时，弹出提示：“请扫描胶片批次码”。
        *   **错误提示 (Toast/Alert)**:
            *   当扫描到非法UID或发生工序错误时，HMI发出声音告警，并显示清晰的错误信息，如“错误：UID不存在！”或“警告：此产品不应经过本工序！”。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **多对多关系**: 一个工序任务（如一炉钢化）会包含多个UID。一个UID会经历多个工序任务。因此，UID与工序任务之间是多对多的关系，需要一个中间表来记录关联。
        *   **数据采集触发器**: 扫描UID是触发数据关联的核心动作。

- **用户故事 (F-PD-3.3)**: 作为一名**质量经理或客服人员**，我想要**通过一个追溯平台，输入任一一片玻璃的UID，就能立刻获得其完整生命周期的所有记录**，以便**快速响应客户问询或进行质量问题分析**。
    - **功能规格 (Functional Specifications)**:
        1.  **查询入口**: 提供一个独立的Web界面——“质量追溯门户”。
        2.  **查询方式**: 支持通过输入或扫描UID进行查询。
        3.  **数据聚合与展示**:
            *   查询成功后，系统需从多个数据源（生产订单、工序任务、报工记录、设备数据、质量数据、WMS数据）中，聚合与该UID相关的所有信息。
            *   展示内容必须结构化、可视化，清晰易懂。
        4.  **报告生成与导出**:
            *   查询结果页面应支持“打印”功能，生成一份格式化的PDF报告。
            *   支持将报告中的关键数据导出为Excel文件。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **查询页面 (Search Page)**:
            *   **输入框 (Input)**: `请输入产品唯一ID (UID)`，支持粘贴和扫码枪输入。
            *   **按钮**: `[查询]`。
        *   **追溯报告页面 (Traceability Report View)**:
            *   **报告头部**: 展示该UID最核心的信息：`产品名称`, `规格`, `所属订单`, `客户`, `当前状态`。
            *   **时间轴视图 (Timeline View)**:
                *   以时间为顺序，垂直展示该UID经历的每一个关键节点，如 `原片入库` -> `切割` -> `磨边` -> `钢化` -> `检验` -> `入库` -> `发货`。
                *   每个节点都是一个可展开的卡片。
            *   **节点卡片 (Node Card)**:
                *   点击展开后，显示该节点的详细信息，如【钢化】节点卡片中应包含：`操作工`, `设备`, `开始/结束时间`。
                *   **关键数据可视化**: 对于钢化、夹胶等工序，卡片内必须内嵌显示其实际的**工艺参数曲线图**（如温度-时间曲线）。
                *   **链接**: 提供到关联文档的链接，如 `[查看IQC报告]`, `[查看FQC检验单]`。
            *   **操作按钮**: `[打印报告]`, `[导出Excel]`。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **数据权限**: 只有被授予“质量追溯查询”权限的角色才能访问此门户。
        *   **数据准确性**: 门户展示的数据必须是生产数据库中的真实数据，不允许是缓存或延迟数据。
        *   **关联查询**: 后端需要有高效的数据库查询设计（如通过UID作为主键或索引），以保证在秒级响应查询结果。

### F-PD-4: 设备物联(IIoT)与数据驱动决策 (IIoT & Data-Driven Decisions)
该模块旨在打通MES与物理设备层之间的双向数据流，实现对设备状态的实时监控、生产指令的自动下发和海量过程数据的价值挖掘。

- **用户故事 (F-PD-4.1)**: 作为一名**IT/OT工程师**，我想要**系统能提供一个灵活的、分层式的设备集成方案**，以便**将工厂内不同年代、不同协议的设备都能经济高效地接入平台**。
    - **说明**: 此用户故事主要描述的是系统的后端能力和集成策略，UI/UX层面涉及较少，主要是提供一个设备管理的后台界面。
    - **功能规格 (Functional Specifications)**:
        1.  **设备资产管理**:
            *   提供一个后台管理界面，用于增、删、改、查工厂中的设备资产。
            *   每个设备需要记录以下信息：`设备名称`, `设备编码`, `资产编号`, `所属工作中心`, `设备型号`, `制造商`, `启用日期`等。
        2.  **数据点位表 (Data Point/Tag) 管理**:
            *   允许为每个设备定义其关键的数据采集点位表。
            *   每个点位需定义：`点位名称` (如“主轴电流”)，`点位地址` (如OPC-UA的NodeID)，`数据类型` (如Float, Boolean)，`单位` (如A, °C)，`采集频率`。
        3.  **连接状态监控**:
            *   在设备管理界面，需要实时显示每个设备的网络连接状态（`在线`/`离线`）。
            *   当设备离线超过设定时间（如3分钟），系统应生成一条告警日志。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **设备管理后台界面**:
            *   **表格 (Table)**: 展示所有设备列表，列头包括 `设备名称`, `设备编码`, `工作中心`, `连接状态`。
            *   **操作按钮**: `[新增设备]`, `[编辑]`, `[删除]`。
            *   **钻取/详情页**: 点击列表项，进入设备详情页。
        *   **设备详情页**:
            *   **表单 (Form)**: 展示和编辑设备的基本信息。
            *   **标签页 (Tab)**:
                *   `[基本信息]`
                *   `[数据点位表]`: 以表格形式管理该设备的所有数据点位。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **分层集成逻辑**: L1/L2/L3的集成方式主要由后端和OT层面实现，前端界面仅做统一的模型管理。
        *   **数据标准化**: 无论通过何种方式采集，数据都应被转换为标准化的格式存入时序数据库。

- **用户故事 (F-PD-4.2)**: 作为一名**生产总监**，我想要**实时监控全厂核心设备的OEE（设备综合效率）**，并能**下钻分析导致效率损失的根本原因**，以便**持续改善生产效率**。
    - **功能规格 (Functional Specifications)**:
        1.  **OEE自动计算**:
            *   系统后台服务需根据设备状态信号（运行/停止）和产量信号，结合班次日历，自动计算每个设备的OEE三大因子。
            *   **时间稼动率**: `(计划运行时长 - 总停机时长) / 计划运行时长`。停机时长按原因（计划停机/故障停机/待料停机等）分类累计。
            *   **性能稼动率**: `(标准节拍 * 总产量) / 实际运行时长`。标准节拍需在设备主数据中维护。
            *   **质量合格率**: `合格品数 / 总产量`。合格品数和总产量来源于工序报工记录。
        2.  **OEE驾驶舱**:
            *   提供一个专门的OEE驾驶舱页面。
            *   支持按时间范围（`今日`/`本周`/`本月`）和组织层级（`全厂`/`某车间`/`某设备`）进行筛选和下钻。
        3.  **可视化呈现**:
            *   **总体OEE**: 以醒目的仪表盘（Gauge）或数字卡片展示所选范围的综合OEE。
            *   **三大因子对比**: 以三个独立的仪表盘或条形图展示时间、性能、质量三大稼动率。
            *   **OEE趋势**: 以折线图展示OEE在选定时间范围内的变化趋势。
        4.  **损失分析 (Loss Analysis)**:
            *   **停机损失分析**: 以柏拉图（Pareto Chart）的形式，展示在选定时间范围内，由各种停机原因造成的时长损失，帮助用户定位最主要的停机原因。
            *   **性能损失分析**: （高级功能，本次迭代简化）展示主要的速度损失原因。
            *   **质量损失分析**: 以柏拉图展示各种不合格原因造成的数量损失。

    - **界面元素与交互定义 (UI Elements & Interaction)**:
        *   **OEE驾驶舱页面**:
            *   **筛选器 (Filter Bar)**: `日期范围选择器`, `层级选择器 (全厂/车间/设备)`。
            *   **布局**: 采用响应式网格布局，包含多个数据可视化卡片。
            *   **仪表盘 (Gauge Chart)**: 用于显示OEE及三大因子的瞬时值。低于阈值时应变色告警。
            *   **折线图 (Line Chart)**: 用于显示OEE历史趋势。
            *   **柏拉图 (Pareto Chart)**:
                *   **交互**: 鼠标悬停在图表的条形上时，显示该原因的具体损失时长或数量。
        *   **下钻交互**:
            *   在层级选择器中选择“某车间”，所有图表数据自动更新为该车间的数据。
            *   在柏拉图上点击某个主要原因（如“设备故障”），可钻取到该原因下的设备列表或详细日志。

    - **业务规则与数据逻辑 (Business Rules & Data Logic)**:
        *   **数据来源**: OEE计算的数据源是标准化的设备状态历史、产量历史和质量报工历史。
        *   **计算周期**: OEE可以按班次、天、周、月等不同周期进行聚合计算。
        *   **标准工时定义**: 计划运行时长来源于后台配置的班次日历，需剔除计划的休息和用餐时间。

- **用户故事 (F-PD-4.3)**: 作为一名**设备工程师**，我想要**系统能基于设备运行数据进行健康度评估，并提前预警潜在的故障**，以便**将维修工作从“被动响应”变为“主动预防”**。
    - **详细描述**:
        - **正常流程**:
            1.  **健康度建模**: 系统持续监控关键部件的运行参数（如电机电流、轴承振动、油压等），并通过算法建立其正常运行的基线模型。
            2.  **异常侦测与预警**: 当实时数据偏离正常基线并达到预警阈值时，系统自动触发“预测性维护”警报。
            3.  **智能诊断建议**: 警报中可包含基于规则或算法的初步诊断建议（如“主轴电流异常增大，可能原因：轴承磨损或负载过大”）。
            4.  **闭环管理**: 预警可自动触发创建设备维护工单，并通知设备工程师进行处理。
        - **异常流程**:
            - 频繁的误报警应能触发系统对模型进行重新学习和校准。
    - **[!] 说明**: 此功能为V2.1及更高版本规划，本次迭代范围不包含其前端界面实现。
---
## 5. 核心实体关系与数据模型 (Core Entity Relationship & Data Model)
本章节定义了MES V2.0核心业务所涉及的关键数据实体及其关系，这将是数据库设计和API定义的基础。

### 5.1 核心实体关系图 (ERD)
```mermaid
erDiagram
    CUSTOMER ||--o{ SALES_ORDER : places
    SALES_ORDER ||--|{ PRODUCTION_ORDER : "generates"
    PRODUCTION_ORDER ||--|{ WORK_ORDER_TASK : "decomposes to"
    WORK_ORDER_TASK ||--|{ TASK_REPORT_LOG : "has"
    WORK_ORDER_TASK ||--o{ UID_TRACE_LOG : "processed by"
    WORK_ORDER_TASK ||--|{ DEVICE_DOWNTIME_LOG : "has"
    DEVICE ||--|{ WORK_ORDER_TASK : "is assigned to"
    USER ||--|{ TASK_REPORT_LOG : "reports"
    PRODUCT ||--o{ PRODUCTION_ORDER : "produces"
    PRODUCT_UID ||--o{ UID_TRACE_LOG : "is"

    PRODUCTION_ORDER {
        string prod_order_id PK
        string sales_order_id FK
        string product_id FK
        int quantity
        date due_date
        string status
    }

    WORK_ORDER_TASK {
        string task_id PK
        string prod_order_id FK
        int sequence
        string operation_name
        string work_center_id FK
        datetime scheduled_start_time
        datetime scheduled_end_time
        datetime actual_start_time
        datetime actual_end_time
        string status
    }

    PRODUCT_UID {
        string uid PK
        string product_id FK
        string status
        datetime creation_time
    }

    UID_TRACE_LOG {
        string log_id PK
        string uid FK
        string task_id FK
        string user_id FK
        datetime scan_time
        json associated_data
    }
```

### 5.2 关键实体详细字段

#### 1. **生产工序任务 (Work_Order_Task)**
| 字段名 | 数据类型 | 是否可空 | 描述 |
| :--- | :--- | :--- | :--- |
| `task_id` | string | N | **主键**, 任务唯一ID |
| `prod_order_id` | string | N | **外键**, 关联到生产订单 |
| `sequence` | int | N | 工序号, 定义在工艺路线中的顺序 |
| `operation_name` | string | N | 工序名称, 如“切割”, “钢化” |
| `work_center_id` | string | N | **外键**, 计划执行的工作中心/设备ID |
| `scheduled_start_time` | datetime | Y | APS计算出的计划开始时间 |
| `scheduled_end_time` | datetime | Y | APS计算出的计划结束时间 |
| `actual_start_time` | datetime | Y | 操作员实际点击“开工”的时间 |
| `actual_end_time` | datetime | Y | 操作员实际点击“完成报工”的时间 |
| `status` | string | N | 任务状态, 枚举: `待排程`, `已下发`, `执行中`, `已完成`, `已暂停` |
| `standard_cycle_time` | float | N | 标准单件生产节拍（秒） |

#### 2. **产品唯一ID (Product_UID)**
| 字段名 | 数据类型 | 是否可空 | 描述 |
| :--- | :--- | :--- | :--- |
| `uid` | string | N | **主键**, 全局唯一身份ID |
| `product_id` | string | N | **外键**, 关联到产品主数据 |
| `source_task_id` | string | N | **外键**, 关联到生成此UID的源头任务（如切割任务） |
| `status` | string | N | UID状态, 枚举: `预生成`, `已绑定`, `已作废`, `已入库`, `已发货` |
| `last_scan_time` | datetime | Y | 最后一次被扫描的时间 |
| `last_scan_location` | string | Y | 最后一次被扫描的工位 |

#### 3. **UID追溯日志 (UID_Trace_Log)**
| 字段名 | 数据类型 | 是否可空 | 描述 |
| :--- | :--- | :--- | :--- |
| `log_id` | string | N | **主键**, 日志唯一ID |
| `uid` | string | N | **外键**, 关联到产品UID |
| `task_id` | string | N | **外键**, 关联到本次操作所属的工序任务 |
| `user_id` | string | N | **外键**, 关联到执行操作的用户 |
| `device_id` | string | N | **外键**, 关联到执行操作的设备 |
| `scan_time` | datetime | N | 扫描发生的时间 |
| `associated_data` | json | Y | 关联的额外数据, 如工艺曲线ID, 消耗的辅料批次号等 |

---

## 6. 核心业务流程与系统集成 (Core Workflow & System Integration)

### 6.1 V2.0 核心业务流程图
新的流程强调了APS引擎的决策核心地位和数据的闭环流动。

```mermaid
graph TD
    subgraph "外部系统"
        A[ERP: 销售订单确认]
        B[PLM: 工艺路线/BOM固化]
    end

    subgraph "MES: 计划层"
        C[订单与工艺数据同步]
        D[F-PD-1: 智能生产调度中心]
        D_1["1. 订单自动分解<br/>(生成待排程任务池)"]
        D_2["2. 定义排程场景<br/>(圈选任务、设定目标/约束)"]
        D_3["3. 启动APS引擎<br/>(含切割优化)"]
        D_4["4. 生成可视化计划<br/>(甘特图)"]
        D_5["5. 人工干预与模拟<br/>(What-If)"]
        D_6["6. 锁定并下发计划"]
    end

    subgraph "MES: 执行层"
        E[F-PD-2: 智能车间执行中心]
        E_1["1. 工位HMI接收任务队列"]
        E_2["2. 扫码开工<br/>(查阅SOP/图纸)"]
        E_3["3. 扫码关联UID<br/>(F-PD-3)"]
        E_4["4. 过程数据自动采集<br/>(F-PD-4)"]
        E_5["5. 报工/异常上报"]
    end
    
    subgraph "MES: 数据与分析"
        F[数据中台]
        F_1["质量追溯数据<br/>(F-PD-3)"]
        F_2["OEE/设备效率数据<br/>(F-PD-4)"]
        G[BI驾驶舱/追溯门户]
    end

    A --> C
    B --> C
    C --> D_1
    D_1 --> D_2
    D_2 --> D_3
    D_3 --> D_4
    D_4 --> D_5
    D_5 --> D_6
    D_6 --> E_1
    E_1 --> E_2
    E_2 --> E_3
    E_3 --> E_4
    E_4 --> E_5
    E_5 --> F
    F --> F_1
    F --> F_2
    F_1 --> G
    F_2 --> G
    E_5 -- 进度/状态/实耗 --> A
```

### 6.2 系统生态集成
MES作为连接计划层与执行层的中枢，其价值的充分发挥，依赖于与企业其他信息系统的无缝集成。

#### **与ERP的深度集成 (ERP Integration)**
- **ERP -> MES (计划的输入)**
    - **销售订单**: 当销售订单在ERP中被确认，且其关联的生产BOM在PLM中被“固化”后，该订单信息被推送到MES的“待分解任务池”。
    - **物料主数据**: ERP是物料主数据的唯一来源，MES实时同步所有物料的基本信息。
    - **库存数据**: MES实时或准实时地从ERP（或WMS）获取物料的可用库存量，作为APS排程的核心约束。
- **MES -> ERP (执行的回溯)**
    - **生产进度反馈**: MES将生产订单和关键工序的状态（如：已排程、生产中、已完工）实时回传给ERP，供销售、客服等角色查询订单状态。
    - **精确实耗上报**: 工序完成报工后，MES将该工序**实际消耗**的物料批次和数量回传给ERP，用于精确的成本核算，取代传统的BOM标准成本法。
    - **工时数据上报**: MES将经确认的、与生产订单关联的工时数据回传给ERP的HR模块，作为计算计件工资和人工成本的依据（本次迭代暂不实现）。

#### **与PLM/CAD/CAM的集成 (PLM/CAD/CAM Integration)**
- **PLM/CAD -> MES (设计的落地)**
    - **工艺文件**: PLM中与产品/工序关联的最新版技术文档（如图纸、SOP），其链接或文件本身被推送到MES，并在工位终端上供操作员查阅。
    - **CAM程序**: 由