# PRD-07: 财务管理子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: <PERSON>oo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
财务数据是企业经营的最终成绩单。然而，当业务与财务脱节时，财务部门往往需要花费大量精力进行手工对账和成本核算，导致数据滞后、口径不一，无法及时、准确地反映经营状况。业财一体化旨在打通业务系统与财务系统之间的数据壁垒，让每一笔业务发生时，都能自动、同步地在财务上产生回响。本子系统旨在构建一个强大的财务核算与管理平台，成为整个ERP系统的“总账本”和“决策驾驶舱”。

### 1.2 商业价值
- **实现业财同步**: 业务单据自动生成财务凭证，确保财务数据与业务活动实时同步，消除信息孤岛。
- **精细化成本核算**: 自动从生产、采购、人事等模块归集成本要素，实现对每一张生产订单、每一种产品的成本的精准核算，为定价和利润分析提供依据。
- **加强资金管控**: 通过对应收、应付账款的实时追踪和账龄分析，加强现金流管理，降低坏账风险。
- **提升决策效率**: 自动生成三大财务报表（资产负债表、利润表、现金流量表），为管理层提供及时、准确的决策支持。

### 1.3 关键目标
- **目标1**: 建立标准的总账体系，实现凭证的自动化处理和账簿的规范化管理。
- **目标2**: 实现应收（AR）和应付（AP）模块与销售、采购业务的无缝集成。
- **目标3**: 构建灵活的成本中心和成本核算模型，自动完成料、工、费的归集与分配。
- **目标4**: 支持多组织核算，能够按法人、事业部、生产中心等维度独立出具财务报表，并支持合并报表。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 赵会计
- **角色**: **财务主管 / 成本会计**
- **背景**: 负责公司的总账、应收应付及成本核算工作。每月初都深陷在收集业务单据、手工录入凭证、用Excel分摊成本的繁重工作中。
- **核心诉求**:
    - "我希望仓库一入库，系统就能自动生成一张暂估应付凭证，而不是等供应商发票来了我再一笔笔录。"
    - "月底算成本简直是噩梦，我希望能有一个按钮，系统就能自动把这个月所有生产订单的材料费、人工费都算出来。"
    - "老板随时都可能要看最新的利润表，我希望能随时从系统里一键生成，而不是加班加点地做表。"

### 2.2 核心场景
- **场景一：销售出库自动生成应收凭证**
  1.  **触发**: 仓库根据销售订单，完成发货出库操作。
  2.  **系统响应**:
      - WMS系统记录出库后，自动触发财务接口。
      - 财务系统根据预设的会计科目模板（如：借：应收账款-客户A，贷：主营业务收入），自动生成一张记账凭证草稿。
      - 财务人员（如赵会计）在“凭证中心”看到这张草稿，审核无误后，点击“过账”。
  3.  **结果**: 该客户的应收账款余额实时增加，公司的收入也同步确认。
  4.  **期望**: 业务完成即财务入账，无需人工干预，保证了应收账款的及时性和准确性。

- **场景二：月末生产成本自动核算**
  1.  **触发**: 月末，所有业务单据处理完毕。
  2.  **操作路径**:
      - 成本会计赵会计进入【财务管理】->【成本核算】->【成本计算】。
      - 选择需要核算的会计期间（如“本月”）。
      - 点击“开始计算”。
  3.  **系统响应**:
      - **1. 归集直接材料**: 系统自动汇总本月所有生产订单从WMS领用的物料成本。
      - **2. 归集直接人工**: 系统根据MES记录的工序报工工时，以及人事系统中设定的工价，计算出人工成本。
      - **3. 归集制造费用**: 归集本月发生的水电、折旧等间接费用。
      - **4. 成本分配**: 系统将直接材料和直接人工计入对应的生产订单成本。将制造费用按预设的分配标准（如按工时）分配到各个生产订单。
      - **5. 成本结转**: 计算出每个生产订单的单位成本，并将已完工入库的成品成本，从“生产成本”科目结转至“库存商品”科目。
  4.  **期望**: 复杂的成本计算过程由系统全自动完成，快速、准确，并能出具详细的成本计算分析报告。

- **场景三：客户付款与应收核销**
  1.  **触发**: 公司收到客户A支付的货款10万元。
  2.  **操作路径**:
      - 赵会计进入【财务管理】->【应收管理】->【收款单】。
      - 新建一张收款单，选择客户A，输入收款金额10万元。
      - 系统自动列出客户A所有未核销的应收账单（发货单）。
      - 赵会计勾选对应的账单，进行核销。
      - 保存并审核收款单。
  3.  **系统响应**:
      - 系统自动生成一张收款凭证（借：银行存款，贷：应收账款-客户A）。
      - 客户A的应收账款余额减少10万元。
  4.  **期望**: 核销过程清晰、直观，能有效管理客户的信用和回款情况。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 总账管理 (F-FI-01)
- **用户故事 (F-FI-01-01)**: 作为一名**财务人员**，我想要**维护会计科目表并管理记账凭证**，以便**进行标准的复式记账**。
  - **详细描述**:
    - **正常流程**:
      1. 支持多级会计科目的自定义设置。
      2. 提供凭证录入、查询、修改、删除、审核、过账、反过账等全套功能。
      3. 支持业务系统（销、采、存）按预设模板自动生成凭证。
      4. 提供总分类账、明细分类账、多栏账等标准账簿的查询和打印。
      5. 支持月末自动进行损益结转、期末调汇等。

### 3.2 应收管理 (AR) (F-FI-02)
- **用户故事 (F-FI-02-01)**: 作为一名**应收会计**，我想要**系统能根据销售发货单自动生成应收账款**，以便**及时、准确地追踪客户欠款**。
  - **详细描述**:
    - **正常流程**:
      1. 与销售、仓储模块集成，销售出库后自动生成应收单。
      2. 管理收款单，支持与应收单的多对多核销。
      3. 提供客户对账单的查询和打印。
      4. 提供账龄分析报告，预警超期应收款。

### 3.3 应付管理 (AP) (F-FI-03)
- **用户故事 (F-FI-03-01)**: 作为一名**应付会计**，我想要**系统能根据采购入库单自动生成暂估应付**，并在收到发票后进行结算，以便**精确管理供应商账务**。
  - **详细描述**:
    - **正常流程**:
      1. 与采购、仓储模块集成，采购入库后自动生成暂估应付单。
      2. 支持录入供应商发票，并与入库单进行核对。
      3. 管理付款单，支持与应付单的核销。
      4. 提供供应商对账单和账龄分析报告。

### 3.4 成本核算 (F-FI-04)
- **用户故事 (F-FI-04-01)**: 作为一名**成本会计**，我想要**系统能自动归集料、工、费，并核算到生产订单**，以便**精确掌握产品成本**。
  - **详细描述**:
    - **正常流程**:
      1. **成本归集**: 自动从WMS获取领料成本，从MES获取工时，从总账获取制造费用。
      2. **成本分配**: 支持按工时、按产量、按面积等多种分配标准设置制造费用分配模型。
      3. **成本计算**: 提供月末一键式的成本计算功能，自动完成上述归集和分配过程。
      4. **报表分析**: 生成生产成本计算单、成本构成分析表等报表。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **准确性**: 财务数据必须100%准确，所有计算逻辑必须经过严格测试和验证。
- **安全性**: 财务数据是企业的核心机密，必须有严格的权限控制和操作审计日志。
- **合规性**: 系统需符合目标市场的会计准则和法规要求。
- **性能**: 月末成本计算和报表生成，在1000张生产订单的规模下，应在10分钟内完成。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **会计科目 (Account)**
    - `account_id` (PK)
    - `account_code` (Unique)
    - `account_name`
    - `balance_direction` (借/贷)
    - ...

2.  **记账凭证头 (Voucher_Header)**
    - `voucher_id` (PK)
    - `voucher_number`
    - `posting_date`
    - `status` (草稿, 已审核, 已过账)
    - `source_system` (AR, AP, GL)
    - ...

3.  **记账凭证行 (Voucher_Line)**
    - `voucher_line_id` (PK)
    - `voucher_id` (FK)
    - `account_id` (FK)
    - `debit_amount`
    - `credit_amount`
    - `summary`
    - ...

4.  **应收/应付单 (Receivable_Payable_Doc)**
    - `doc_id` (PK)
    - `doc_type` (AR/AP)
    - `partner_id` (客户/供应商)
    - `source_doc_id` (出库单/入库单)
    - `amount`
    - `unapplied_amount` (未核销金额)
    - ...

5.  **成本中心 (Cost_Center)**
    - `cost_center_id` (PK)
    - `cost_center_code`
    - `name`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 业财一体化总览
```mermaid
graph TD
    subgraph 业务系统
        A[销售出库] --> B[生成应收单];
        C[采购入库] --> D[生成应付单];
        E[生产领料/报工] --> F[记录成本要素];
    end
    subgraph 财务系统
        G[凭证模板]
        H[应收模块]
        I[应付模块]
        J[成本模块]
        K[总账模块]
        L[财务报表]
    end

    B -- 自动生成凭证 --> H;
    D -- 自动生成凭证 --> I;
    F -- 月末归集 --> J;
    H -- 过账 --> K;
    I -- 过账 --> K;
    J -- 成本结转凭证 --> K;
    K --> L;
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-FI-01 (应收凭证自动生成)**:
  - **Given** 仓库完成了一笔金额为5000元的销售发货
  - **When** 该出库单在WMS中确认
  - **Then** 财务系统的凭证中心必须自动生成一张对应的记账凭证草稿：借：应收账款 5000，贷：主营业务收入 5000。

- **AC-FI-02 (成本归集)**:
  - **Given** 一张生产订单本月领用了价值1000元的材料，发生了200元的人工
  - **When** 我在成本模块查看该订单的成本构成
  - **Then** 系统必须能准确显示其直接材料成本为1000元，直接人工成本为200元。

- **AC-FI-03 (应付核销)**:
  - **Given** 我有一笔10000元的应付账款，并收到一张8000元的付款单
  - **When** 我用付款单核销该应付账款
  - **Then** 该应付账款的未核销余额必须准确地变为2000元。