# PRD-02: 工艺管理子系统 (PDM) 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: Roo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
在玻璃深加工行业，产品具有高度定制化、规格参数极其复杂的特点。一个销售订单往往包含数百种尺寸、厚度、加工工艺各不相同的产品。传统依赖人工和Excel管理产品结构（BOM）和工艺路线的方式，不仅效率低下、错误率高，而且无法将设计知识有效沉淀和复用，严重制约了企业的标准化和规模化发展。工艺管理子系统（PDM）旨在解决这一核心痛点，为企业建立一个统一、规范、可视化的产品数据管理平台。

### 1.2 商业价值
- **知识沉淀与复用**: 将产品设计、工艺参数、加工方法等核心知识结构化、数字化，形成企业可复用的核心资产。
- **设计生产一体化**: 打通从产品设计到生产制造的数据流，确保生产部门能够准确无误地获取产品数据和工艺要求，是实现C2M（Customer-to-Manufactory）模式的基础。
- **提升报价与下单效率**: 通过参数化BOM，销售人员或客户可以快速配置产品、计算物料成本，从而实现快速、准确的报价和下单。
- **降低生产错误率**: 消除因图纸、工艺单信息传递错误导致的生产事故，保证产品质量。

### 1.3 关键目标
- **目标1**: 建立统一的物料主数据管理中心，管理原材料、半成品、成品等所有物料信息。
- **目标2**: 实现对玻璃产品复杂、多变的产品结构（BOM）的有效管理，支持参数化、可配置的BOM。
- **目标3**: 实现对生产工艺路线的标准化管理，能够定义工序、工作中心，并为不同产品配置不同的加工路线。
- **目标4.0**: 管理与产品和工艺相关的技术文档（如图纸、SOP），并实现版本控制。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 李工
- **角色**: **工艺工程师 / 技术部经理**
- **背景**: 公司的技术核心，负责新产品的设计、工艺路线的制定、BOM的创建与维护。对玻璃加工的各种工艺了如指掌。
- **核心诉求**:
    - "我希望能有一个工具，让我可以像搭积木一样快速构建一个新产品的BOM，而不是每次都从零开始画图、算料。"
    - "当某个原材料更换供应商导致尺寸变化时，我希望能快速定位到所有使用到该材料的BOM，并进行批量更新。"
    - "我需要确保车间拿到的工艺卡片永远是最新、最准确的版本。"

### 2.2 核心场景
- **场景一：创建一种新的中空玻璃产品**
  1.  **触发**: 销售部接到一个新项目，需要一种规格为“5mm钢化白玻 + 12A + 5mm Low-E”的中空玻璃。
  2.  **操作路径**:
      - 工艺工程师李工登录系统，进入【工艺管理】->【BOM管理】模块。
      - 选择“新建BOM”，BOM类型选择“成品BOM”。
      - 在BOM头信息中，输入成品物料的编码和描述。
      - 在BOM行信息中，开始添加构成物料：
          - **第1行**: 添加物料“5mm白玻原片”，输入定额数量（按面积计算），并选择加工工艺为“切割”、“磨边”、“钻孔”、“钢化”。
          - **第2行**: 添加物料“12mm铝条”，输入定额数量（按周长计算）。
          - **第3行**: 添加物料“丁基胶”、“分子筛”等辅料，输入定额。
          - **第4行**: 添加物料“5mm Low-E原片”，选择加工工艺“切割”、“磨边”、“钢化”。
      - 定义BOM的参数化规则：将玻璃的“长”和“高”定义为变量，物料的定额数量与这两个变量关联。
      - 保存并审核该BOM。
  3.  **期望**: BOM创建过程可视化，物料和工艺可以方便地从库中选取。参数化规则定义直观。

- **场景二：定义防火窗的工艺路线**
  1.  **触发**: 公司开始生产一种新型号的防火窗。
  2.  **操作路径**:
      - 李工进入【工艺管理】->【工艺路线】模块。
      - 新建一条工艺路线，命名为“防火窗标准生产路线”。
      - 在路线中，按顺序添加工序：
          - **工序10**: 铝型材切割（工作中心：切割中心）。
          - **工序20**: 窗框组装（工作中心：组装中心）。
          - **工序30**: 玻璃生产（工作中心：玻璃车间，或标记为**可外协**）。
          - **工序40**: 成品组装（将窗框和玻璃组装）。
          - **工序50**: 质检（工作中心：质检中心）。
          - **工序60**: 包装入库。
      - 为每个工序设置标准的工时、准备时间等。
      - 将此工艺路线与“防火窗”系列的所有成品BOM进行关联。
  3.  **期望**: 工艺路线的定义清晰，支持并行、分支和外协工序。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 物料主数据管理 (F-PDM-01)
- **用户故事 (F-PDM-01-01)**: 作为一名**工艺工程师**，我想要**维护一个统一的物料库**，以便**为BOM和工艺设计提供标准化的物料信息**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、查询、禁用物料。
      2. 物料属性应全面，包括：物料编码、名称规格、物料类型（原材料、半成品、成品、辅料）、基本单位、采购单位、库存单位、默认供应商、成本信息等。
      3. 对玻璃原片等关键物料，应有特定属性：长、宽、厚度、颜色、品级。
      4. 物料编码应支持自动生成规则。
    - **异常流程**:
      - 已产生业务数据（如库存、订单）的物料，关键信息（如编码、类型）应被锁定，不允许修改。

### 3.2 产品结构 (BOM) 管理 (F-PDM-02)
- **用户故事 (F-PDM-02-01)**: 作为一名**工艺工程师**，我想要**创建和管理多版本的BOM**，以便**追踪产品的设计变更历史**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、复制、编辑、查询BOM。
      2. BOM应有版本控制，每次重大修改可生成新版本，老版本可追溯。只有一个版本是当前激活的生产版本。
      3. BOM应区分为：成品BOM、半成品BOM、虚拟件BOM等类型。
    - **异常流程**:
      - 已被生产订单引用的BOM版本，应被锁定，不允许修改，只能升版。

- **用户故事 (F-PDM-02-02)**: 作为一名**工艺工程师**，我想要**为BOM定义参数化规则**，以便**销售或下单时能根据输入的尺寸等参数自动计算物料用量**。
  - **详细描述**:
    - **正常流程**:
      1. 支持在BOM中定义变量（如 `H` 代表高度，`W` 代表宽度）。
      2. BOM行中物料的用量可以使用包含这些变量的公式来表示（如玻璃面积 `(H*W)/1000000`，铝条周长 `(H+W)*2/1000`）。
      3. 在销售订单等环节，输入具体参数值后，系统能自动计算出准确的物料需求。
    - **异常流程**:
      - 公式语法错误时，系统应给出明确提示。

- **用户故事 (F-PDM-02-03)**: 作为一名**工艺工程师**，我想要**审核并“固化”由销售订单传递过来的BOM配置**，以便**生成一个准确无误的、可用于生产的最终BOM**。
  - **详细描述**:
    - **正常流程**:
      1. 销售订单确认后，系统根据销售配置自动生成一个“BOM快照”，并将其状态标记为“待审核”。
      2. 工艺工程师可以对这个BOM快照进行微调，如更换替代料、增加生产辅料、调整工艺损耗等。
      3. 审核通过后，该BOM被“固化”（锁定），并正式关联到销售订单行，成为后续生产和采购的唯一依据。这个固化的BOM独立于原始的参数化BOM模板。
    - **异常流程**:
      - 如果审核发现重大问题，可将BOM驳回至销售环节。

### 3.3 工艺路线管理 (F-PDM-03)
- **用户故事 (F-PDM-03-01)**: 作为一名**工艺工程师**，我想要**定义标准化的工艺路线**，以便**规范产品的生产流程和工序**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、查询工艺路线。
      2. 工艺路线由一系列有序的工序组成。
      3. 工序信息应包含：工序号、工序名称、工作中心、准备工时、加工工时、是否可外协、质检要求等。
      4. 支持将工艺路线与物料（成品/半成品）进行关联。
    - **异常流程**:
      - 工艺路线被生产订单引用后，应被锁定，不允许修改，只能升版。

### 3.4 技术文档管理 (F-PDM-04)
- **用户故事 (F-PDM-04-01)**: 作为一名**工艺工程师**，我想要**将CAD图纸、作业指导书等文件关联到物料或BOM上**，以便**生产人员可以方便地查阅**。
  - **详细描述**:
    - **正常流程**:
      1. 支持上传、下载、预览各类技术文档（如DWG, PDF, Word, Excel）。
      2. 支持将文档与物料、BOM、工序进行关联。
      3. 支持文档版本控制。
    - **异常流程**:
      - 上传文件大小和类型应可配置限制。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: 参数化BOM在10个变量内，输入参数后计算物料清单的响应时间应小于1秒。
- **安全性**: 核心技术资料（BOM、工艺路线、图纸）的访问和修改权限需严格控制。
- **可用性**: 系统需保证7x24小时可用，核心功能可用性不低于99.9%。
- **集成性**: PDM数据需能无缝传递给销售、采购、生产等下游系统。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **物料主数据 (Item_Master)**
    - `item_id` (PK)
    - `item_code` (Unique)
    - `item_name`
    - `specification`
    - `item_type` (原材料, 半成品, 成品)
    - `base_unit`
    - `attributes` (JSON, for custom properties like color, thickness)
    - ...

2.  **BOM头 (Bom_Header)**
    - `bom_id` (PK)
    - `item_id` (FK to Item_Master, the parent item)
    - `version`
    - `status` (Active, Inactive, Archived)
    - `parameters` (JSON, defines variables like H, W)
    - ...

3.  **BOM行 (Bom_Line)**
    - `bom_line_id` (PK)
    - `bom_id` (FK to Bom_Header)
    - `child_item_id` (FK to Item_Master)
    - `quantity_formula` (String, e.g., "(H+W)*2/1000")
    - `scrap_rate`
    - ...

4.  **工艺路线头 (Routing_Header)**
    - `routing_id` (PK)
    - `routing_code`
    - `description`
    - `version`
    - ...

5.  **工艺路线行 (Routing_Line)**
    - `routing_line_id` (PK)
    - `routing_id` (FK)
    - `sequence` (工序号)
    - `operation_name`
    - `work_center_id`
    - `setup_time`
    - `run_time`
    - `is_subcontracted` (Boolean)
    - ...

6.  **技术文档 (Technical_Document)**
    - `doc_id` (PK)
    - `doc_name`
    - `file_path`
    - `version`
    - `linked_object_type` (Item, BOM, Routing)
    - `linked_object_id`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 从销售配置到生产BOM的业务流程
```mermaid
graph TD
    subgraph 工艺设计阶段
        A[工艺工程师: 创建参数化BOM模板]
    end
    subgraph 销售报价阶段
        B[销售员: 创建报价单] --> C[选择参数化产品, 输入长宽等参数];
        C --> D{系统根据BOM模板实时生成<br/>'销售BOM' (成本与报价)};
    end
    subgraph 订单确认阶段
        E[客户确认报价] --> F[报价单转为销售订单];
        F --> G[系统生成'待审核BOM快照'];
    end
    subgraph 工艺确认阶段
        H[工艺工程师: 审核BOM快照] --> I{可微调<br/>(如增减辅料)};
        I --> J[确认并'固化'为'生产BOM'];
    end
    subgraph 生产执行阶段
        K[生产BOM] --> L[驱动MRP、采购、生产任务];
    end

    A --> C;
    D --> E;
    G --> H;
    J --> K;
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-PDM-01 (参数化BOM创建)**:
  - **Given** 我是工艺工程师
  - **When** 我创建一个玻璃门的BOM，将高度(H)和宽度(W)设为参数，并将玻璃用量公式设为 `H*W`
  - **Then** 系统能成功保存此BOM，且在BOM查看界面能清晰地看到参数和公式。

- **AC-PDM-02 (参数化BOM应用)**:
  - **Given** 存在一个高度(H)和宽度(W)参数化的玻璃门BOM
  - **When** 销售人员在订单中选择此玻璃门，并输入H=2000, W=800
  - **Then** 系统必须能自动计算出玻璃的需求量为1,600,000平方毫米（或1.6平方米）。

- **AC-PDM-03 (工艺路线关联)**:
  - **Given** 我已为“钢化玻璃”物料关联了包含“切割->磨边->钢化”的工艺路线
  - **When** 生产部门下达“钢化玻璃”的生产任务
  - **Then** 系统生成的生产工单上必须清晰地列出“切割”、“磨边”、“钢化”这三道工序及其要求。