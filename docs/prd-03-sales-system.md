# PRD-03: 销售管理子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: <PERSON>oo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
玻璃深加工行业的核心业务始于销售订单。当前企业面临的最大挑战是订单处理效率低下、错误率高。业务员使用Excel管理订单，不仅录入工作量巨大（一个订单常有数百个不同规格的明细），而且价格计算复杂、易出错。更重要的是，订单信息无法顺畅地传递到生产、采购等后续环节，形成了信息孤岛。本子系统旨在打造一个高效、精准、协同的销售订单管理中心，彻底解决上述痛点。

### 1.2 商业价值
- **提升订单处理效率**: 通过高效的订单录入工具和参数化配置，将复杂订单的录入时间缩短80%以上。
- **实现精准报价与成本控制**: 在报价和下单环节，实时、准确地计算物料成本，确保报价的竞争力和利润空间。
- **打通销产协同**: 将确认后的订单（包含已固化的生产BOM）无缝传递给生产和采购部门，消除信息壁垒，实现快速响应。
- **提升客户满意度**: 快速响应客户的报价和订单需求，准确交付，提升客户体验和忠诚度。

### 1.3 关键目标
- **目标1**: 提供一个功能强大的订单配置器，支持基于参数化BOM的快速产品配置和报价。
- **目标2**: 实现从报价单到销售订单的平滑转换，并集成“销售BOM”到“生产BOM”的审核固化流程。
- **目标3**: 打造一个极致高效的订单录入界面，支持类Excel操作和批量导入，以应对海量订单明细。
- **目标4**: 建立灵活的价格管理体系，支持多种定价策略。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 王经理
- **角色**: **销售经理 / 销售代表**
- **背景**: 公司销售团队的核心成员，每天需要处理大量的客户询价和订单。对产品熟悉，但对复杂的计算感到头疼。
- **核心诉求**:
    - "我希望能有一个工具，让我面对客户询价时，能在几分钟内就给出一份八九不离十的报价单。"
    - "客户一个订单发过来一个几百行的Excel，我希望能一键导入系统，而不是一个个手动敲。"
    - "订单下给工厂后，我希望能实时看到它的生产进度，方便我给客户一个准确的交期答复。"

### 2.2 核心场景
- **场景一：为客户快速制作报价单**
  1.  **触发**: 客户需要为一批淋浴房玻璃门进行报价。
  2.  **操作路径**:
      - 销售王经理登录系统，进入【销售管理】->【报价单管理】，新建报价单。
      - 填写客户信息后，进入产品明细行。
      - 点击“添加产品”，选择“标准淋浴房门（参数化）”。
      - 系统弹出配置界面，王经理输入客户要求的尺寸：H=1900, W=750。
      - 系统根据P_DM中定义的BOM，自动计算出所需玻璃、五金件的成本，并根据定价策略生成建议报价。王经理可在此基础上微调。
      - 添加多行不同规格的产品后，保存报价单，并通过系统生成PDF发送给客户。
  3.  **期望**: 整个报价过程流畅、快速，报价结果准确，无需线下使用计算器和Excel。

- **场景二：处理一个包含数百种规格的幕墙玻璃订单**
  1.  **触发**: 客户发来一个Excel文件，包含300种不同尺寸的幕墙玻璃订单。
  2.  **操作路径**:
      - 王经理进入【销售管理】->【销售订单管理】，新建销售订单。
      - 在订单明细界面，点击“从Excel导入”。
      - 下载系统模板，将客户的Excel数据整理粘贴到模板中（主要包含：产品编码/名称、长、宽、数量、工艺要求等列）。
      - 上传该Excel文件。
  3.  **系统响应**:
      - 系统批量解析文件内容，自动在订单中生成300行订单明细。
      - 对于每一行，系统自动根据其尺寸和工艺要求，匹配参数化BOM，计算出价格和物料清单。
      - 如果有任何行解析失败（如产品不存在、尺寸超限），系统会给出明确的错误报告，方便王经理定位和修改。
  4.  **期望**: 几分钟内即可完成海量订单的录入和价格计算。

- **场景三：订单确认与BOM固化**
  1.  **触发**: 客户确认了场景一中的淋浴房报价，决定下单。
  2.  **操作路径**:
      - 王经理在系统中找到对应的报价单，点击“转为销售订单”。
      - 系统自动生成一份销售订单，并继承所有信息。王经理补充了交货地址等信息后，提交订单。
      - 订单状态变为“待工艺审核”。
  3.  **系统响应**:
      - 系统自动为该订单生成一个“BOM快照”，并向工艺部发送审核通知。
      - 工艺工程师李工（见PDM PRD）在待办事项中看到此任务，打开并审核BOM，进行微调后“固化”为生产BOM。
      - BOM固化后，销售订单状态自动更新为“待生产”，并准备向下游传递。
  4.  **期望**: 销售与工艺部门之间流程衔接顺畅，责任明确，订单信息准确无误地进入生产环节。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 报价单管理 (F-SL-01)
- **用户故事 (F-SL-01-01)**: 作为一名**销售代表**，我想要**使用产品配置器快速创建和管理报价单**，以便**高效响应客户询价**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、复制、查询、作废报价单。
      2. 报价单明细支持添加标准产品和参数化产品。
      3. 调用参数化产品时，能自动加载配置界面，并根据输入的参数，调用PDM子系统的能力实时计算成本和价格。
      4. 支持报价单版本管理，可多次报价。
      5. 支持将报价单导出为标准格式的PDF文件。
      6. 支持一键将已确认的报价单转为销售订单。
    - **异常流程**:
      - 报价单超过有效期后，应自动变为“已过期”状态。

### 3.2 销售订单管理 (F-SL-02)
- **用户故事 (F-SL-02-01)**: 作为一名**销售代表**，我想要**通过高效的方式创建和管理销售订单**，以便**准确地将客户需求录入系统**。
  - **详细描述**:
    - **正常流程**:
      1. 支持手动创建、从报价单转单、从Excel导入等多种方式创建订单。
      2. 订单界面应采用高性能表格控件，支持类似Excel的增删改查、复制粘贴、批量填充等操作。
      3. 订单应有清晰的状态管理：草稿、待审核、待生产、生产中、已发货、已完成、已关闭。
      4. 订单提交后，自动触发向工艺部门的“BOM固化”审核流程。
      5. **智能导入功能 (分阶段实现)**:
         - **(一期)** **格式自动清洗**: 系统在导入时，自动去除所有单元格内容前后的多余空格。
         - **(一期)** **产品别名匹配**: 导入时，若产品名称无法精确匹配，系统将自动查询“产品别名库”进行二次匹配。别名库由工艺或管理员在PDM子系统中维护。
         - **(二期)** **数据校验与建议**: 为关键参数（如长、宽）设置合理范围。对超出范围的数据，系统在错误报告中高亮并提供“数值异常”的警告。
         - **(二期)** **默认值填充**: 对模板中为空的非关键字段，系统可尝试使用产品主数据中预设的默认值进行填充，并向用户提示。
    - **异常流程**:
      - 导入Excel格式错误或数据校验（包括智能校验）失败时，系统应提供一份包含清晰错误原因和所在行数的报告，方便用户定位修改。
      - 订单进入生产环节后，应被锁定，不允许修改关键信息。如需变更，需走订单变更流程。

### 3.3 价格管理 (F-SL-03)
- **用户故事 (F-SL-03-01)**: 作为一名**销售经理**，我想要**维护灵活的价格策略**，以便**系统能根据不同的客户和产品自动计算价格**。
  - **详细描述**:
    - **正常流程**:
      1. 支持建立多套价格本（如标准价、经销商价、大客户价）。
      2. 支持按客户、按产品、按数量区间设置不同的定价。
      3. 支持设置复杂的计价公式，如“按面积计价”、“按周长计价”、“阶梯价”等。
      4. 在报价和订单环节，系统能根据客户和产品信息，自动匹配最优的价格策略。
    - **异常流程**:
      - 若一个产品匹配到多个价格，系统应提示用户选择或采用预设的优先级规则。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: 订单导入功能，在500行明细内，处理时间应小于30秒。订单表格在500行内滚动、编辑不应有卡顿。
- **安全性**: 销售价格、客户成本等属于敏感数据，需严格控制访问权限。
- **可用性**: 订单创建和管理作为核心功能，可用性不低于99.95%。
- **数据一致性**: 必须确保销售订单与下游生产、采购任务的数据完全一致。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **报价单头 (Quote_Header)**
    - `quote_id` (PK)
    - `quote_number` (Unique)
    - `customer_id`
    - `salesperson_id`
    - `status` (草稿, 已报价, 已确认, 已过期)
    - `total_amount`
    - ...

2.  **报价单行 (Quote_Line)**
    - `quote_line_id` (PK)
    - `quote_id` (FK)
    - `item_id` (FK)
    - `parameters` (JSON, 存储配置参数如H, W)
    - `quantity`
    - `unit_price`
    - `line_amount`
    - `bom_snapshot` (JSON, 存储当时计算出的销售BOM)
    - ...

3.  **销售订单头 (Sales_Order_Header)**
    - `order_id` (PK)
    - `order_number` (Unique)
    - `quote_id` (FK, optional)
    - `customer_id`
    - `status` (待审核, 待生产, ...)
    - ...

4.  **销售订单行 (Sales_Order_Line)**
    - `order_line_id` (PK)
    - `order_id` (FK)
    - `item_id` (FK)
    - `parameters` (JSON)
    - `quantity`
    - `unit_price`
    - `production_bom_id` (FK to a locked/versioned BOM in PDM)
    - `status` (待固化, 待生产, ...)
    - ...

5.  **价格本 (Price_List)**
    - `price_list_id` (PK)
    - `name`
    - `currency`
    - ...

6.  **价格规则 (Price_Rule)**
    - `rule_id` (PK)
    - `price_list_id` (FK)
    - `item_id` (FK, optional)
    - `customer_id` (FK, optional)
    - `min_quantity`
    - `price` or `discount_formula`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 复杂订单处理流程
```mermaid
graph TD
    A[客户提供Excel订单] --> B[销售员: 新建销售订单];
    B --> C[使用'Excel导入'功能];
    C --> D{系统批量创建订单行};
    D -- 成功 --> E[销售员确认订单信息];
    D -- 部分失败 --> F[系统生成错误报告];
    F --> G[销售员修改数据后重新导入];
    E --> H[提交订单];
    H --> I[触发'BOM固化'审核流程<br/>(流向工艺部门)];
    I --> J[工艺部门确认生产BOM];
    J --> K[订单状态变为'待生产'];
    K --> L[通知生产和采购部门];
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-SL-01 (参数化报价)**:
  - **Given** 我是销售代表，且系统中存在一个参数化的“淋浴房门”产品
  - **When** 我在报价单中选择该产品，并输入高度=2000，宽度=800
  - **Then** 系统必须在2秒内自动计算出该行的价格和总价。

- **AC-SL-02 (Excel导入)**:
  - **Given** 我有一个包含100行有效产品信息的标准Excel模板文件
  - **When** 我在销售订单页面上传该文件
  - **Then** 系统应在30秒内成功创建包含100行明细的销售订单，且每行的价格都已自动计算。

- **AC-SL-03 (BOM固化流程)**:
  - **Given** 我提交了一张包含参数化产品的销售订单
  - **When** 订单状态为“待工艺审核”
  - **Then** 我作为销售，此时应无法修改该订单的关键信息；同时，工艺工程师的待办列表中必须出现一条对应的BOM审核任务。