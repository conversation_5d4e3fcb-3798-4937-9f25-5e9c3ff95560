# PRD-05: 生产管理子系统（MES）产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**玻璃深加工企业面临高度定制化、小批量、多品种的复杂生产场景，传统手工排程和执行管理无法应对，导致生产效率低下、交期难以保证、质量追溯困难。**

### 1.2 价值主张
建立智能制造执行系统（MES），通过APS智能排程、数字化车间执行和全生命周期质量追溯，实现从"经验驱动"到"数据驱动"的生产管理转型。

### 1.3 商业价值量化
- **生产效率提升**: 智能排程使生产效率提升30%，设备利用率提升25%
- **交期准确率提升**: 从70%提升至95%，客户满意度显著改善
- **质量追溯能力**: 实现单片级别的全流程追溯，满足高端产品认证要求
- **运营成本降低**: 减少人工排程工作80%，降低质量事故损失60%

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **生产计划员** | 负责生产计划制定、APS排程管理 | 需要智能排程工具和计划优化能力 |
| **车间操作员** | 负责设备操作、工序执行、数据采集 | 需要清晰的作业指导和简单的报工界面 |
| **质量工程师** | 负责质量管理、追溯分析、持续改进 | 需要完整的质量追溯和分析工具 |
| **车间主管** | 负责现场管理、进度监控、异常处理 | 需要实时的生产监控和预警系统 |

### 2.2 核心使用场景

#### 场景一：APS智能排程
**用户故事**: 作为一个生产计划员，我想要通过APS引擎自动生成最优生产计划，以便应对复杂的多订单排程需求。

**操作流程**:
1. 系统自动同步ERP中已审核的销售订单
2. 订单自动分解为工序任务并进入待排程任务池
3. 计划员设定优化目标：交期优先、设备利用率最大化
4. 配置约束条件：设备产能、物料可用性、人员技能
5. 启动智能排程，APS引擎生成最优计划
6. 以甘特图形式展示排程结果和关键指标
7. 确认后下发生产计划到各工位

**成功标准**: 50个订单的排程计算在5分钟内完成，计划准确率≥95%

#### 场景二：数字化车间执行
**用户故事**: 作为一个车间操作员，我想要通过数字化终端接收任务和上报进度，以便精准执行生产计划。

**操作流程**:
1. 操作员在工位终端查看任务队列
2. 扫码开始工序任务，系统显示数字化SOP
3. 按照工艺要求执行加工操作
4. 关键工序参数自动采集（温度、压力、时间等）
5. 完成后扫码报工，上报合格/不合格数量
6. 系统自动更新生产进度和库存状态
7. 异常情况触发Andon报警和处理流程

**成功标准**: 报工操作在30秒内完成，数据采集准确率≥99%

#### 场景三：全生命周期质量追溯
**用户故事**: 作为一个质量工程师，我想要追溯任意产品的完整生产过程，以便快速定位质量问题根因。

**操作流程**:
1. 扫描产品上的唯一身份码（UID）
2. 系统展示该产品的数字档案
3. 查看从原片到成品的所有工序记录
4. 包含：操作员、设备、工艺参数、质检结果
5. 关键工序的实时数据曲线（如钢化温度曲线）
6. 关联的物料批次和供应商信息
7. 生成完整的追溯报告

**成功标准**: 追溯查询在30秒内完成，数据完整性100%

---

## 3. 功能需求（用户故事格式）

### 3.1 智能生产调度

#### 需求 3.1.1: 订单自动分解
**用户故事**: 作为一个生产计划员，我想要系统自动将销售订单分解为工序任务，以便为排程提供标准化输入。

**功能描述**:
- 自动同步ERP中已审核的销售订单
- 根据生产BOM和工艺路线分解为工序任务
- 建立工序间的前后置依赖关系
- 生成待排程任务池

**验收标准**:
- [ ] 支持自动和手动触发订单同步
- [ ] 订单分解准确率100%，包含完整的工序信息
- [ ] 异常订单清晰提示并通知相关人员
- [ ] 分解操作具有幂等性，避免重复生成

#### 需求 3.1.2: APS智能排程
**用户故事**: 作为一个生产计划员，我想要配置排程目标和约束，以便生成最优的生产计划。

**功能描述**:
- 支持多目标优化：交期、设备利用率、物料利用率
- 考虑设备产能、物料可用性、人员技能等约束
- 智能算法生成全局最优排程方案
- 甘特图可视化展示排程结果

**验收标准**:
- [ ] 支持拖拽调整优化目标优先级
- [ ] 物料冲突预警和解决建议
- [ ] 排程计算时间：100个工序任务 < 5分钟
- [ ] 排程结果包含关键指标分析

#### 需求 3.1.3: 生产计划下发
**用户故事**: 作为一个生产计划员，我想要将确认的排程计划下发到各工位，以便开始生产执行。

**功能描述**:
- 一键下发排程计划到对应工位
- 工序任务自动推送到工位终端
- 包含完整的工艺参数和作业指导
- 计划变更的实时同步

**验收标准**:
- [ ] 计划下发实时生效，工位终端立即更新
- [ ] 工序任务信息完整，包含工艺参数
- [ ] 支持计划调整和重新下发
- [ ] 下发状态可追踪和确认

### 3.2 数字化车间执行

#### 需求 3.2.1: 工位任务管理
**用户故事**: 作为一个车间操作员，我想要在工位终端查看任务队列，以便按计划执行生产任务。

**功能描述**:
- 工位终端显示当前和待执行任务队列
- 任务信息包含产品、数量、工艺要求
- 支持任务优先级和紧急插单
- 数字化SOP和技术文档查阅

**验收标准**:
- [ ] 任务队列实时更新，优先级清晰
- [ ] 任务信息完整，包含所有必要的生产信息
- [ ] 数字化SOP易于查阅，支持图片和视频
- [ ] 支持任务状态的实时反馈

#### 需求 3.2.2: 扫码报工
**用户故事**: 作为一个车间操作员，我想要通过扫码快速报工，以便准确上报生产进度。

**功能描述**:
- 扫码开始和完成工序任务
- 上报合格数量、不合格数量和废品数量
- 自动记录操作员、时间、设备信息
- 异常情况的快速上报和处理

**验收标准**:
- [ ] 扫码操作响应时间 < 2秒
- [ ] 报工界面简洁，操作步骤最少
- [ ] 自动校验数据合理性，异常提示
- [ ] 支持批量报工和修正操作

#### 需求 3.2.3: 设备数据采集
**用户故事**: 作为一个车间操作员，我想要系统自动采集关键工序参数，以便确保工艺执行的准确性。

**功能描述**:
- 关键设备的实时数据采集
- 工艺参数的自动记录和存储
- 参数异常的实时预警
- 设备状态的监控和反馈

**验收标准**:
- [ ] 支持主要设备类型的数据采集
- [ ] 数据采集频率可配置，精度满足要求
- [ ] 参数超限自动报警，操作员及时响应
- [ ] 数据存储完整，支持历史查询

### 3.3 质量追溯管理

#### 需求 3.3.1: 产品身份管理
**用户故事**: 作为一个质量工程师，我想要为每个产品分配唯一身份标识，以便建立完整的追溯链条。

**功能描述**:
- 产品唯一身份码（UID）的生成和绑定
- 支持二维码、条码等多种标识方式
- 身份码与产品信息的关联管理
- 标识的打印和粘贴流程

**验收标准**:
- [ ] UID生成规则可配置，保证全局唯一性
- [ ] 支持批量生成和打印身份码
- [ ] 身份码与产品信息准确绑定
- [ ] 标识清晰可读，耐用性满足要求

#### 需求 3.3.2: 过程数据关联
**用户故事**: 作为一个质量工程师，我想要将生产过程数据与产品身份关联，以便建立完整的数据档案。

**功能描述**:
- 工序执行数据与产品UID的自动关联
- 操作员、设备、工艺参数的完整记录
- 物料批次和供应商信息的追溯
- 质检结果和异常处理记录

**验收标准**:
- [ ] 过程数据自动关联，无需人工干预
- [ ] 数据记录完整，包含所有关键信息
- [ ] 支持多层级的物料追溯
- [ ] 数据不可篡改，具有审计功能

#### 需求 3.3.3: 追溯查询分析
**用户故事**: 作为一个质量工程师，我想要快速查询产品的追溯信息，以便进行质量分析和问题定位。

**功能描述**:
- 基于UID的快速追溯查询
- 可视化的数字档案展示
- 关键工序数据的图表分析
- 追溯报告的生成和导出

**验收标准**:
- [ ] 追溯查询响应时间 < 30秒
- [ ] 数字档案信息完整，展示清晰
- [ ] 支持工艺曲线等数据的图表展示
- [ ] 追溯报告格式专业，满足客户要求

### 3.4 生产监控预警

#### 需求 3.4.1: 实时生产看板
**用户故事**: 作为一个车间主管，我想要通过电子看板实时监控生产状态，以便及时发现和处理异常。

**功能描述**:
- 生产进度的实时展示
- 设备状态和利用率监控
- 质量指标和异常统计
- 关键KPI的可视化展示

**验收标准**:
- [ ] 看板数据实时更新，延迟 < 30秒
- [ ] 界面清晰直观，关键信息突出
- [ ] 支持多种图表类型和展示方式
- [ ] 异常情况醒目提示，便于快速响应

#### 需求 3.4.2: Andon报警系统
**用户故事**: 作为一个车间操作员，我想要在遇到异常时快速报警，以便获得及时的支持和处理。

**功能描述**:
- 多种异常类型的快速报警
- 报警信息的自动分发和升级
- 异常处理流程的跟踪管理
- 报警数据的统计和分析

**验收标准**:
- [ ] 报警操作简单，响应时间 < 5秒
- [ ] 报警信息准确传达给相关人员
- [ ] 异常处理过程可追踪和记录
- [ ] 支持报警数据的统计分析

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] 订单分解准确率 100%
- [ ] APS排程计算成功率 ≥ 95%
- [ ] 工序报工数据准确率 ≥ 99%
- [ ] 质量追溯数据完整性 100%
- [ ] 设备数据采集成功率 ≥ 98%

### 4.2 性能验收标准
- [ ] 排程计算时间：100个工序任务 < 5分钟
- [ ] 追溯查询响应时间 < 30秒
- [ ] 报工操作响应时间 < 2秒
- [ ] 看板数据更新延迟 < 30秒
- [ ] 系统并发处理能力 ≥ 100用户

### 4.3 业务效果验收标准
- [ ] 生产效率提升 ≥ 30%
- [ ] 设备利用率提升 ≥ 25%
- [ ] 交期准确率提升至 ≥ 95%
- [ ] 质量事故响应时间缩短 ≥ 80%

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **工业化设计**: 界面适合车间环境，支持触摸操作
- **信息层次**: 关键信息突出，次要信息收纳
- **操作简化**: 常用操作步骤最少，支持快捷方式
- **状态清晰**: 系统状态和进度实时反馈

### 5.2 关键界面要求
- **排程甘特图**: 时间轴清晰，支持拖拽调整
- **工位终端**: 大字体显示，触摸友好
- **质量追溯**: 信息层次清晰，支持钻取查看
- **生产看板**: 数据可视化，异常醒目提示

---

## 6. 数据埋点需求

### 6.1 生产行为埋点
- 排程操作和效果
- 报工行为和准确性
- 异常报警和处理
- 质量追溯查询

### 6.2 设备运行埋点
- 设备利用率和OEE
- 工艺参数和异常
- 停机原因和时长
- 能耗和效率指标

### 6.3 业务效果埋点
- 生产效率指标
- 交期达成率
- 质量指标变化
- 成本控制效果

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **预测性维护**: 基于设备数据的故障预测
- **AI优化算法**: 深度学习的排程优化
- **AR作业指导**: 增强现实的操作指导
- **数字孪生**: 生产线的数字化建模

### 7.2 技术演进方向
- **边缘计算**: 车间级的实时数据处理
- **5G应用**: 高速低延迟的数据传输
- **区块链**: 质量数据的不可篡改存储

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅
