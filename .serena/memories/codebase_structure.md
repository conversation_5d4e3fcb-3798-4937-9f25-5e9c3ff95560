# 代码库结构

## 目录结构
```
roo_prd/
├── .serena/           # Serena项目配置
│   └── project.yml
├── .roo/             # 项目配置
│   └── mcp.json
├── docs/             # 原始PRD文档集合
│   ├── prd-00-integration-overview.md
│   ├── prd-01-basic-management-system.md
│   ├── prd-02-pdm-system.md
│   ├── prd-03-sales-system.md
│   ├── prd-04-procurement-system.md
│   ├── prd-05-production-system.md
│   ├── prd-05-production-system-improved.md
│   ├── prd-06-wms-system.md
│   ├── prd-07-finance-system.md
│   ├── prd-08-project-system.md
│   ├── prd-09-quality-system.md
│   ├── prd-10-crm-system.md
│   ├── prd-11-hr-system.md
│   ├── prd-13-data-center.md
│   ├── user-requirement.md
│   ├── prd-quality-checklist.md
│   ├── prd-improvement-summary.md
│   └── before-after-comparison.md
└── prd/              # 重构后的PRD文档目标目录（待创建）
```

## 文档分类

### 核心PRD文档（13个子系统）
1. 系统集成概览 (prd-00)
2. 基础管理子系统 (prd-01)
3. 工艺管理子系统/PDM (prd-02)
4. 销售管理子系统 (prd-03)
5. 采购管理子系统 (prd-04)
6. 生产管理子系统/MES (prd-05)
7. 仓储管理子系统/WMS (prd-06)
8. 财务管理子系统 (prd-07)
9. 项目管理子系统 (prd-08)
10. 质量管理子系统 (prd-09)
11. 客户关系管理子系统/CRM (prd-10)
12. 人事管理子系统/HR (prd-11)
13. 数据中心/BI (prd-13)

### 辅助文档
- 用户需求说明书
- 质量检查清单
- 改进总结报告
- 对比分析文档