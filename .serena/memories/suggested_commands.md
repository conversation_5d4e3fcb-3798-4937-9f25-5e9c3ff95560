# 建议的命令

## 文档管理命令

### 查看文档结构
```bash
# 查看docs目录结构
ls -la docs/

# 查看所有PRD文档
find docs/ -name "prd-*.md" | sort

# 查看文档内容
cat docs/[文档名].md
```

### 文档编辑和验证
```bash
# 使用Markdown编辑器编辑文档
# 推荐使用支持Mermaid图表的编辑器

# 检查Markdown语法
# 可使用markdownlint等工具
```

### 项目管理
```bash
# 创建目录结构
mkdir -p prd/[子系统名称]

# 复制和重构文档
cp docs/prd-XX-[系统名].md prd/[子系统名称]/[新文件名].md
```

### Git版本控制
```bash
# 查看文档变更
git status
git diff

# 提交文档变更
git add .
git commit -m "重构PRD文档: [具体变更说明]"

# 查看提交历史
git log --oneline
```

### 文档质量检查
```bash
# 检查术语一致性
grep -r "术语名称" docs/

# 统计文档行数
wc -l docs/*.md

# 查找重复内容
# 可使用文本比较工具
```