# 代码风格和约定

## 文档编写约定

### 文档结构标准
1. **标题层级**: 使用标准的Markdown标题层级（#, ##, ###等）
2. **术语管理**: 每个PRD文档开头必须包含术语表
3. **章节编号**: 使用数字编号系统（1. 2. 3.等）

### 标准PRD文档结构
```markdown
# PRD-XX: [系统名称] 产品需求文档

## 1. 核心问题与价值主张
## 2. 目标用户与使用场景  
## 3. 功能需求（用户故事格式）
## 4. 验收标准（可测试列表）
## 5. 交互设计要求
## 6. 数据埋点需求
## 7. 未来展望/V-Next
```

### 用户故事格式
```markdown
作为一个 [角色], 我想要 [完成某个目标], 以便 [获得某种价值]
```

### 验收标准格式
- 使用可测试、无二义性的列表形式
- 包含功能、性能、用户体验等维度

### 术语管理
- 英文全称、中文定义和使用说明
- 全文严格使用统一术语
- 避免术语混用